# Use Allman-style
indent_columns=4
indent_braces=false
indent_else_if=false
indent_switch_case=4
indent_label=1
nl_if_brace=add
nl_brace_else=add
nl_elseif_brace=add
nl_else_brace=add
nl_else_if=remove
nl_for_brace=add
nl_while_brace=add
nl_switch_brace=add
nl_fdef_brace=add
nl_do_brace=add
sp_func_proto_paren=Remove
sp_func_def_paren=Remove
sp_func_call_paren=Remove
sp_sizeof_paren=Remove

# No tabs, spaces only
indent_with_tabs=0
align_with_tabs=false
cmt_convert_tab_to_spaces=true

# Do not put spaces between the # and preprocessor statements
pp_space=remove

# Various whitespace fiddling
sp_assign=add
sp_before_sparen=add
sp_inside_sparen=remove
sp_cond_colon=add
sp_cond_question=add
sp_bool=add
sp_else_brace=add
sp_brace_else=add
pos_arith=Lead
pos_bool=Lead
nl_func_type_name=add
nl_before_case=true
nl_assign_leave_one_liners=true
nl_enum_leave_one_liners=true
nl_brace_fparen=add
nl_max=4
nl_after_func_proto=2

# Always use scoping braces for conditionals
mod_full_brace_if=add
mod_full_brace_if_chain=false
mod_full_brace_while=add
mod_full_brace_for=add
mod_full_brace_do=add

# Annotate #else and #endif statements
mod_add_long_ifdef_endif_comment=20
mod_add_long_ifdef_else_comment=5

# Misc cleanup
mod_remove_extra_semicolon=true

# Use C-style comments (/* .. */)
cmt_c_nl_end=true
cmt_star_cont=true
cmt_cpp_to_c=true

# Use "char **a"-style pointer stars/dereferences
sp_before_ptr_star=Add
sp_between_ptr_star=Remove
sp_after_ptr_star=Remove
sp_before_byref=Add
sp_after_byref=Remove
