*.[oa]
*.l[oa]
*.dll
*.exe
*.exe.*
*.obj
*.pyc
*.so
*~
*.idb
*.suo
*.ncb
*.vcproj.*
*.vcxproj.user
*.sln.cache
*.log
Release
Debug
Win32-Output
.vs
.deps
.libs
Makefile
Makefile.in
aclocal.m4
autodefs.h
autom4te.cache
config.guess
config.h
config.h.in
config.log
config.status
config.sub
configure
configure.h
depcomp
doxygen/
stamp-h1
install-sh
missing
ltmain.sh
libtool
m4/libtool.m4
m4/ltoptions.m4
m4/ltsugar.m4
m4/ltversion.m4
m4/lt~obsolete.m4

version.sh
msvc-env-local.bat
config-msvc-local.h
config-msvc-version.h
doc/openvpn.8.html
distro/rpm/openvpn.spec
distro/systemd/*.service
sample/sample-keys/sample-ca/
vendor/.build
vendor/dist
build/msvc/msvc-generate/version.m4

tests/t_client.sh
tests/t_client-*-20??????-??????/
t_client.rc
t_client_ips.rc
tests/unit_tests/**/*_testdriver

src/openvpn/openvpn
include/openvpn-plugin.h
config-version.h
nbproject
test-driver
compile
stamp-h2
