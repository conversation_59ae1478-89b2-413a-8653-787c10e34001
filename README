OpenVPN -- A Secure tunneling daemon

Copyright (C) 2002-2018 OpenVPN Inc. This program is free software;
you can redistribute it and/or modify
it under the terms of the GNU General Public License version 2
as published by the Free Software Foundation.

*************************************************************************

To get the latest release of OpenVPN, go to:

	https://openvpn.net/index.php/download/community-downloads.html

To Build and Install,

	tar -zxf openvpn-<version>.tar.gz
	cd openvpn-<version>
	./configure
	make
	make install

or see the file INSTALL for more info.

*************************************************************************

For detailed information on OpenVPN, including examples, see the man page
  http://openvpn.net/man.html

For a sample VPN configuration, see
  http://openvpn.net/howto.html

To report an issue, see
  https://community.openvpn.net/openvpn/report

For a description of OpenVPN's underlying protocol,
  see the file ssl.h included in the source distribution.

*************************************************************************

Other Files & Directories:

* configure.ac -- script to rebuild our configure
  script and makefile.

* sample/sample-scripts/verify-cn

  A sample perl script which can be used with OpenVPN's
  --tls-verify option to provide a customized authentication
  test on embedded X509 certificate fields.

* sample/sample-keys/

  Sample RSA keys and certificates.  DON'T USE THESE FILES
  FOR ANYTHING OTHER THAN TESTING BECAUSE THEY ARE TOTALLY INSECURE.

* sample/sample-config-files/

  A collection of OpenVPN config files and scripts from
  the HOWTO at http://openvpn.net/howto.html

*************************************************************************

Note that easy-rsa and tap-windows are now maintained in their own subprojects.
Their source code is available here:

  https://github.com/OpenVPN/easy-rsa
  https://github.com/OpenVPN/tap-windows

The old cross-compilation environment (domake-win) and the Python-based
buildsystem have been replaced with openvpn-build:

  https://github.com/OpenVPN/openvpn-build

See the INSTALL file for usage information.
