#!/bin/sh
#
#  Bring down vpn routing.

#  calculate the network address
remote_network=`ipcalc -n "$remote"/"$remote_netmask_bits"`
remote_network="${remote_network#*=}"

#  clear routing via VPN
ip route del "$remote_network"/"$remote_netmask_bits" via "$5" table vpn.out
ip route del table vpnonly.out via "$5"
iptables -D OUTPUT -t mangle -p "$proto" \
		-d "$remote_network"/"$remote_netmask_bits" \
		--dport "$remote_port" -j ACCEPT
iptables -D OUTPUT -t mangle -d "$remote" -j MARK --set-mark 2

#  undo the ICMP ping tunneling
iptables -D OUTPUT -t mangle --protocol icmp --icmp-type echo-request \
		-j MARK --set-mark 3

#  flush route cache
ip route flush cache
