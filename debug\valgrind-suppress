{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_udp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:getpwnam_r
   fun:getpwnam
   fun:get_user
   fun:management_open
   fun:open_management
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   fun:__nss_next
   fun:gethostbyname_r
   fun:gethostbyname
   fun:getaddr
   fun:resolve_remote
   fun:link_socket_init_phase1
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:gethostbyname_r
   fun:gethostbyname
   fun:getaddr
   fun:resolve_remote
   fun:link_socket_init_phase1
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libdl-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libdl-2.5.so
   fun:dlopen
   fun:plugin_list_init
   fun:init_plugins
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libdl-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libdl-2.5.so
   fun:dlopen
   fun:plugin_list_init
   fun:init_plugins
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/libdl-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libdl-2.5.so
   fun:dlsym
   fun:libdl_resolve_symbol
   fun:plugin_list_init
   fun:init_plugins
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libdl-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libdl-2.5.so
   fun:dlopen
   fun:plugin_list_init
   fun:init_plugins
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/libc-2.7.so
   obj:/lib/ld-2.7.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.7.so
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/libc-2.7.so
   obj:/lib/ld-2.7.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.7.so
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_udp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/libc-2.7.so
   obj:/lib/ld-2.7.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.7.so
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:management_open
   fun:open_management
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Addr8
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/ld-2.7.so
   obj:/lib/libc-2.7.so
   obj:/lib/ld-2.7.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.7.so
   fun:getpwnam_r
   fun:getpwnam
   fun:get_user
   fun:management_open
   fun:open_management
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_div
   fun:BN_MONT_CTX_set
   fun:BN_MONT_CTX_set_locked
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:RSA_verify
   fun:EVP_VerifyFinal
   fun:ASN1_item_verify
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:X509_verify_cert
   fun:ssl_verify_cert_chain
   fun:ssl3_get_client_certificate
   fun:ssl3_accept
   fun:ssl3_read_bytes
   fun:ssl3_read
   obj:/usr/lib/libssl.so.0.9.8
   fun:BIO_read
   fun:bio_read
   fun:tls_process
   fun:tls_multi_process
   fun:check_tls_dowork
   fun:pre_select
   fun:multi_process_post
   fun:multi_process_incoming_link
   fun:multi_tcp_action
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_div
   fun:BN_MONT_CTX_set
   fun:BN_MONT_CTX_set_locked
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:ssl3_ctx_ctrl
   fun:init_ssl
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_div
   fun:BN_MONT_CTX_set
   fun:BN_MONT_CTX_set_locked
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:ssl3_ctx_ctrl
   fun:init_ssl
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_udp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_div
   fun:BN_MONT_CTX_set
   fun:BN_MONT_CTX_set_locked
   obj:/usr/lib/libcrypto.so.0.9.8
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:RSA_sign
   fun:ssl3_send_server_key_exchange
   fun:ssl3_accept
   fun:ssl3_read_bytes
   fun:ssl3_read
   obj:/usr/lib/libssl.so.0.9.8
   fun:BIO_read
   fun:bio_read
   fun:tls_process
   fun:tls_multi_process
   fun:check_tls_dowork
   fun:pre_select
   fun:multi_process_post
   fun:multi_process_incoming_link
   fun:multi_tcp_action
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_div
   fun:BN_MONT_CTX_set
   fun:BN_mod_exp_mont
   fun:BN_BLINDING_create_param
   fun:RSA_setup_blinding
   obj:/usr/lib/libcrypto.so.0.9.8
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:RSA_sign
   fun:ssl3_send_server_key_exchange
   fun:ssl3_accept
   fun:ssl3_read_bytes
   fun:ssl3_read
   obj:/usr/lib/libssl.so.0.9.8
   fun:BIO_read
   fun:bio_read
   fun:tls_process
   fun:tls_multi_process
   fun:check_tls_dowork
   fun:pre_select
   fun:multi_process_post
   fun:multi_process_incoming_link
   fun:multi_tcp_action
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_div
   fun:BN_nnmod
   fun:BN_mod_inverse
   fun:BN_MONT_CTX_set
   fun:BN_MONT_CTX_set_locked
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:ssl3_ctx_ctrl
   fun:init_ssl
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_div
   fun:BN_nnmod
   fun:BN_mod_inverse
   fun:BN_MONT_CTX_set
   fun:BN_MONT_CTX_set_locked
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:ssl3_ctx_ctrl
   fun:init_ssl
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_udp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_mod_inverse
   fun:BN_MONT_CTX_set
   fun:BN_MONT_CTX_set_locked
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:RSA_verify
   fun:EVP_VerifyFinal
   fun:ASN1_item_verify
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:X509_verify_cert
   fun:ssl_verify_cert_chain
   fun:ssl3_get_client_certificate
   fun:ssl3_accept
   fun:ssl3_read_bytes
   fun:ssl3_read
   obj:/usr/lib/libssl.so.0.9.8
   fun:BIO_read
   fun:bio_read
   fun:tls_process
   fun:tls_multi_process
   fun:check_tls_dowork
   fun:pre_select
   fun:multi_process_post
   fun:multi_process_incoming_link
   fun:multi_tcp_action
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_mod_inverse
   fun:BN_MONT_CTX_set
   fun:BN_MONT_CTX_set_locked
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:ssl3_ctx_ctrl
   fun:init_ssl
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_mod_inverse
   fun:BN_MONT_CTX_set
   fun:BN_MONT_CTX_set_locked
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:ssl3_ctx_ctrl
   fun:init_ssl
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_udp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_mod_inverse
   fun:BN_MONT_CTX_set
   fun:BN_MONT_CTX_set_locked
   obj:/usr/lib/libcrypto.so.0.9.8
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:RSA_sign
   fun:ssl3_send_server_key_exchange
   fun:ssl3_accept
   fun:ssl3_read_bytes
   fun:ssl3_read
   obj:/usr/lib/libssl.so.0.9.8
   fun:BIO_read
   fun:bio_read
   fun:tls_process
   fun:tls_multi_process
   fun:check_tls_dowork
   fun:pre_select
   fun:multi_process_post
   fun:multi_process_incoming_link
   fun:multi_tcp_action
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_mod_inverse
   fun:BN_MONT_CTX_set
   fun:BN_mod_exp_mont
   fun:BN_BLINDING_create_param
   fun:RSA_setup_blinding
   obj:/usr/lib/libcrypto.so.0.9.8
   obj:/usr/lib/libcrypto.so.0.9.8
   fun:RSA_sign
   fun:ssl3_send_server_key_exchange
   fun:ssl3_accept
   fun:ssl3_read_bytes
   fun:ssl3_read
   obj:/usr/lib/libssl.so.0.9.8
   fun:BIO_read
   fun:bio_read
   fun:tls_process
   fun:tls_multi_process
   fun:check_tls_dowork
   fun:pre_select
   fun:multi_process_post
   fun:multi_process_incoming_link
   fun:multi_tcp_action
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:*
   obj:*
   obj:*
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   fun:__nss_next
   fun:gethostbyname_r
   fun:gethostbyname
   fun:getaddr
   fun:resolve_remote
   fun:link_socket_init_phase1
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:gethostbyname_r
   fun:gethostbyname
   fun:getaddr
   fun:resolve_remote
   fun:link_socket_init_phase1
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   fun:__nss_next
   fun:gethostbyname_r
   fun:gethostbyname
   fun:getaddr
   fun:resolve_remote
   fun:link_socket_init_phase1
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:gethostbyname_r
   fun:gethostbyname
   fun:getaddr
   fun:resolve_remote
   fun:link_socket_init_phase1
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   fun:__nss_next
   fun:gethostbyname_r
   fun:gethostbyname
   fun:getaddr
   fun:resolve_remote
   fun:link_socket_init_phase1
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_udp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:gethostbyname_r
   fun:gethostbyname
   fun:getaddr
   fun:resolve_remote
   fun:link_socket_init_phase1
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/ld-2.5.so
   fun:__libc_dlopen_mode
   fun:__nss_lookup_function
   obj:/lib/libc-2.5.so
   fun:getpwnam_r
   fun:getpwnam
   fun:get_user
   fun:management_open
   fun:open_management
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libc-2.5.so
   obj:/lib/libdl-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libdl-2.5.so
   fun:dlsym
   fun:libdl_resolve_symbol
   fun:plugin_list_init
   fun:init_plugins
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libdl-2.5.so
   obj:/lib/ld-2.5.so
   obj:/lib/libdl-2.5.so
   fun:dlopen
   fun:plugin_list_init
   fun:init_plugins
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:CRYPTO_malloc
   fun:sk_new
   obj:/usr/lib/libssl.so.0.9.8
   fun:SSL_COMP_get_compression_methods
   fun:SSL_library_init
   fun:init_ssl_lib
   fun:init_static
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:__nss_lookup_function
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:__nss_lookup_function
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:__nss_lookup_function
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_udp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:__nss_lookup_function
   obj:*
   obj:*
   fun:getpwnam_r
   fun:getpwnam
   fun:get_user
   fun:management_open
   fun:open_management
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:getdelim
   fun:getpass
   fun:get_console_input
   fun:get_user_pass
   fun:context_init_1
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:tsearch
   fun:__nss_lookup_function
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:tsearch
   fun:__nss_lookup_function
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:tsearch
   fun:__nss_lookup_function
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_udp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:tsearch
   fun:__nss_lookup_function
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:management_open
   fun:open_management
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   fun:tsearch
   fun:__nss_lookup_function
   obj:*
   obj:*
   fun:getpwnam_r
   fun:getpwnam
   fun:get_user
   fun:management_open
   fun:open_management
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   obj:/lib/libc-2.5.so
   fun:__nss_database_lookup
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   obj:/lib/libc-2.5.so
   fun:__nss_database_lookup
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   obj:/lib/libc-2.5.so
   fun:__nss_database_lookup
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_udp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   obj:/lib/libc-2.5.so
   fun:__nss_database_lookup
   obj:*
   obj:*
   fun:getpwnam_r
   fun:getpwnam
   fun:get_user
   fun:management_open
   fun:open_management
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   obj:/lib/libc-2.7.so
   fun:__nss_database_lookup
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_tcp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   obj:/lib/libc-2.7.so
   fun:__nss_database_lookup
   obj:*
   obj:*
   fun:getgrnam_r
   fun:getgrnam
   fun:get_group
   fun:do_init_first_time
   fun:init_instance
   fun:init_instance_handle_signals
   fun:tunnel_server_udp
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Leak
   fun:malloc
   obj:/lib/libc-2.7.so
   fun:__nss_database_lookup
   obj:*
   obj:*
   fun:getpwnam_r
   fun:getpwnam
   fun:get_user
   fun:management_open
   fun:open_management
   fun:main
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_mod_inverse
}

{
   <insert a suppression name here>
   Memcheck:Cond
   fun:BN_div
}
