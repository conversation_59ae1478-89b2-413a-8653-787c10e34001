#ifdef HAVE_CONFIG_H
#include <config.h>
#else
#include <config-msvc-version.h>
#endif
#include <winresrc.h>

#pragma code_page(65001) /* UTF8 */

LANGUAGE LANG_NEUTRAL, SUBLANG_NEUTRAL

VS_VERSION_INFO VERSIONINFO
 FILEVERSION OPENVPN_VERSION_RESOURCE
 PRODUCTVERSION OPENVPN_VERSION_RESOURCE
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x40004L
 FILETYPE 0x2L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "The OpenVPN Project"
            VALUE "FileDescription", "OpenVPN Daemon"
            VALUE "FileVersion", PACKAGE_VERSION ".0"
            VALUE "InternalName", "OpenVPN"
            VALUE "LegalCopyright", "Copyright © The OpenVPN Project" 
            VALUE "OriginalFilename", "openvpn.exe"
            VALUE "ProductName", "OpenVPN"
            VALUE "ProductVersion", PACKAGE_VERSION ".0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
