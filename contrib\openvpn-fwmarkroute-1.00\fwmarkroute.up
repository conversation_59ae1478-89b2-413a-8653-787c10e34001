#!/bin/sh
#
#  Bring up vpn routing.

#  calculate the network address
remote_network=`ipcalc -n "$remote"/"$remote_netmask_bits"`
remote_network="${remote_network#*=}"

#  add the stuff that doesn't change if it's not already there
grep -q '^202 ' /etc/iproute2/rt_tables 
if [ "$?" -ne 0 ]
then
	echo 202 vpn.out >> /etc/iproute2/rt_tables
fi
grep -q '^203 ' /etc/iproute2/rt_tables 
if [ "$?" -ne 0 ]
then
	echo 203 vpnonly.out >> /etc/iproute2/rt_tables
fi
ip rule ls | grep -q 'lookup vpn.out *$'
if [ "$?" -ne 0 ]
then
	ip rule add fwmark 2 table vpn.out
fi
ip rule ls | grep -q 'lookup vpnonly.out *$'
if [ "$?" -ne 0 ]
then
	ip rule add fwmark 3 table vpnonly.out
fi

#  route VPN traffic using the normal table
iptables -A OUTPUT -t mangle -p "$proto" -d "$remote" --dport "$remote_port" \
		-j ACCEPT

#  route all other traffic to that host via VPN
iptables -A OUTPUT -t mangle -d "$remote_network"/"$remote_netmask_bits" \
		-j MARK --set-mark 2

#  route all ICMP pings over the VPN
iptables -A OUTPUT -t mangle --protocol icmp --icmp-type echo-request \
		-j MARK --set-mark 3

#  NAT traffic going over the VPN, so it doesn't have an unknown address
iptables -t nat -A POSTROUTING -o "$1" -j SNAT --to-source "$4"

#  add routing commands
ip route add "$remote_network"/"$remote_netmask_bits" via "$5" table vpn.out
ip route add table vpnonly.out via "$5"
ip route flush cache
