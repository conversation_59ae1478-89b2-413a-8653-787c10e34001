#!/bin/sh

#
# Build an OpenVPN plugin module on *nix.  The argument should
# be the base name of the C source file (without the .c).
#

# This directory is where we will look for openvpn-plugin.h
CPPFLAGS="${CPPFLAGS:--I../../../include}"

CC="${CC:-gcc}"
CFLAGS="${CFLAGS:--O2 -Wall -g}"

$CC $CPPFLAGS $CFLAGS -fPIC -c $1.c && \
$CC $CFLAGS -fPIC -shared ${LDFLAGS} -Wl,-soname,$1.so -o $1.so $1.o -lc
