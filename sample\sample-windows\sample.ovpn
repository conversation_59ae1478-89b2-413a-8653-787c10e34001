# Edit this file, and save to a .ovpn extension
# so that OpenVPN will activate it when run
# as a service.

# Change 'myremote' to be your remote host,
# or comment out to enter a listening
# server mode.
remote myremote

# Uncomment this line to use a different
# port number than the default of 1194.
; port 1194

# Choose one of three protocols supported by
# OpenVPN.  If left commented out, defaults
# to udp.
; proto [tcp-server | tcp-client | udp]

# You must specify one of two possible network
# protocols, 'dev tap' or 'dev tun' to be used
# on both sides of the connection.  'tap' creates
# a VPN using the ethernet protocol while 'tun'
# uses the IP protocol.  You must use 'tap'
# if you are ethernet bridging or want to route
# broadcasts.  'tun' is somewhat more efficient
# but requires configuration of client software
# to not depend on broadcasts.  Some platforms
# such as Solaris, OpenBSD, and Mac OS X only
# support 'tun' interfaces, so if you are
# connecting to such a platform, you must also
# use a 'tun' interface on the Windows side.

# Enable 'dev tap' or 'dev tun' but not both!
dev tap

# This is a 'dev tap' ifconfig that creates
# a virtual ethernet subnet.
# ******** is the local VPN IP address
# and ************* is the VPN subnet.
# Only define this option for 'dev tap'.
ifconfig ******** *************

# This is a 'dev tun' ifconfig that creates
# a point-to-point IP link.
# ******** is the local VPN IP address and
# ******** is the remote VPN IP address. 
# Only define this option for 'dev tun'.
# Make sure to include the "tun-mtu" option
# on the remote machine, but swap the order
# of the ifconfig addresses.
;tun-mtu 1500
;ifconfig ******** ********

# If you have fragmentation issues or misconfigured
# routers in the path which block Path MTU discovery,
# lower the TCP MSS and internally fragment non-TCP
# protocols.
;fragment 1300
;mssfix

# If you have set up more than one TAP-Win32 adapter
# on your system, you must refer to it by name.
;dev-node my-tap

# You can generate a static OpenVPN key
# by selecting the Generate Key option
# in the start menu.
#
# You can also generate key.txt manually
# with the following command:
#   openvpn --genkey --secret key.txt
#
# key must match on both ends of the connection,
# so you should generate it on one machine and
# copy it to the other over a secure medium.
# Place key.txt in the same directory as this
# config file.
secret key.txt

# Uncomment this section for a more reliable
# detection when a system loses its connection.
# For example, dial-ups or laptops that travel
# to other locations.
#
# If this section is enabled and "myremote"
# above is a dynamic DNS name (i.e. dyndns.org),
# OpenVPN will dynamically "follow" the IP
# address of "myremote" if it changes.
; ping-restart 60
; ping-timer-rem
; persist-tun
; persist-key
; resolv-retry 86400

# keep-alive ping
ping 10

# enable LZO compression
comp-lzo

# moderate verbosity
verb 4
mute 10
