#!/bin/sh
#
# openvpn       This shell script takes care of starting and stopping
#               openvpn on RedHat or other chkconfig-based system.
#
# chkconfig: 345 24 76
#
# description: OpenVPN is a robust and highly flexible tunneling application \
#              that uses all of the encryption, authentication, and \
#              certification features of the OpenSSL library to securely \
#              tunnel IP networks over a single UDP port.
#

# Contributed to the OpenVPN project by
# <PERSON> <<EMAIL>>
# 2002.05.15

# To install:
#   copy this file to /etc/rc.d/init.d/openvpn
#   shell> chkconfig --add openvpn
#   shell> mkdir /etc/openvpn
#   make .conf or .sh files in /etc/openvpn (see below)

# To uninstall:
#   run: chkconfig --del openvpn

# Author's Notes:
#
# I have created an /etc/init.d init script and enhanced openvpn.spec to
# automatically register the init script.  Once the RPM is installed you
# can start and stop OpenVPN with "service openvpn start" and "service
# openvpn stop".
#
# The init script does the following:
#
# - Starts an openvpn process for each .conf file it finds in
#   /etc/openvpn.
#
# - If /etc/openvpn/xxx.sh exists for a xxx.conf file then it executes
#   it before starting openvpn (useful for doing openvpn --mktun...).
#
# - In addition to start/stop you can do:
#
#   service openvpn reload - SIGHUP
#   service openvpn reopen - SIGUSR1
#   service openvpn status - SIGUSR2
#
# Modifications:
#
# 2003.05.02
#   * Changed == to = for sh compliance (Bishop Clark).
#   * If condrestart|reload|reopen|status, check that we were
#     actually started (James Yonan).
#   * Added lock, piddir, and work variables (James Yonan).
#   * If start is attempted twice, without an intervening stop, or
#     if start is attempted when previous start was not properly
#     shut down, then kill any previously started processes, before
#     commencing new start operation (James Yonan).
#   * Do a better job of flagging errors on start, and properly
#     returning success or failure status to caller (James Yonan).
#
# 2005.04.04
#   * Added openvpn-startup and openvpn-shutdown script calls
#     (James Yonan).
#

# Location of openvpn binary
openvpn=""
openvpn_locations="/usr/sbin/openvpn /usr/local/sbin/openvpn"
for location in $openvpn_locations
do
  if [ -f "$location" ]
  then
    openvpn=$location
  fi
done

# Lockfile
lock="/var/lock/subsys/openvpn"

# PID directory
piddir="/var/run/openvpn"

# Our working directory
work=/etc/openvpn

# Source function library.
. /etc/rc.d/init.d/functions

# Source networking configuration.
. /etc/sysconfig/network

# Check that networking is up.
if [ "${NETWORKING}" = "no" ]
then
  echo "Networking is down"
  exit 0
fi

# Check that binary exists
if ! [ -f  $openvpn ] 
then
  echo "openvpn binary not found"
  exit 0
fi

# See how we were called.
case "$1" in
  start)
	echo -n $"Starting openvpn: "

	/sbin/modprobe tun >/dev/null 2>&1

	# From a security perspective, I think it makes
	# sense to remove this, and have users who need
	# it explictly enable in their --up scripts or
	# firewall setups.

	#echo 1 > /proc/sys/net/ipv4/ip_forward

	# Run startup script, if defined
	if [ -f $work/openvpn-startup ]; then
	    $work/openvpn-startup
	fi

	if [ ! -d  $piddir ]; then
	    mkdir $piddir
	fi

	if [ -f $lock ]; then
	    # we were not shut down correctly
	    for pidf in `/bin/ls $piddir/*.pid 2>/dev/null`; do
	      if [ -s $pidf ]; then
		kill `cat $pidf` >/dev/null 2>&1
	      fi
	      rm -f $pidf
	    done
	    rm -f $lock
	    sleep 2
	fi

	rm -f $piddir/*.pid
	cd $work

	# Start every .conf in $work and run .sh if exists
	errors=0
	successes=0
	for c in `/bin/ls *.conf 2>/dev/null`; do
	    bn=${c%%.conf}
	    if [ -f "$bn.sh" ]; then
		. ./$bn.sh
	    fi
	    rm -f $piddir/$bn.pid
	    $openvpn --daemon --writepid $piddir/$bn.pid --config $c --cd $work
	    if [ $? = 0 ]; then
		successes=1
	    else
		errors=1
	    fi
	done

	if [ $errors = 1 ]; then
	    failure; echo
	else
	    success; echo
	fi

	if [ $successes = 1 ]; then
	    touch $lock
	fi
	;;
  stop)
	echo -n $"Shutting down openvpn: "
	for pidf in `/bin/ls $piddir/*.pid 2>/dev/null`; do
	  if [ -s $pidf ]; then
	    kill `cat $pidf` >/dev/null 2>&1
	  fi
	  rm -f $pidf
	done

	# Run shutdown script, if defined
	if [ -f $work/openvpn-shutdown ]; then
	    $work/openvpn-shutdown
	fi

	success; echo
	rm -f $lock
	;;
  restart)
	$0 stop
	sleep 2
	$0 start
	;;
  reload)
	if [ -f $lock ]; then
	    for pidf in `/bin/ls $piddir/*.pid 2>/dev/null`; do
		if [ -s $pidf ]; then
		    kill -HUP `cat $pidf` >/dev/null 2>&1
		fi
	    done
	else
	    echo "openvpn: service not started"
	    exit 1
	fi
	;;
  reopen)
	if [ -f $lock ]; then
	    for pidf in `/bin/ls $piddir/*.pid 2>/dev/null`; do
		if [ -s $pidf ]; then
		    kill -USR1 `cat $pidf` >/dev/null 2>&1
		fi
	    done
	else
	    echo "openvpn: service not started"
	    exit 1
	fi
	;;
  condrestart)
	if [ -f $lock ]; then
	    $0 stop
	    # avoid race
	    sleep 2
	    $0 start
	fi
	;;
  status)
	if [ -f $lock ]; then
	    for pidf in `/bin/ls $piddir/*.pid 2>/dev/null`; do
		if [ -s $pidf ]; then
		    kill -USR2 `cat $pidf` >/dev/null 2>&1
		fi
	    done
	    echo "Status written to /var/log/messages"
	else
	    echo "openvpn: service not started"
	    exit 1
	fi
        ;;
  *)
	echo "Usage: openvpn {start|stop|restart|condrestart|reload|reopen|status}"
	exit 1
	;;
esac
exit 0
