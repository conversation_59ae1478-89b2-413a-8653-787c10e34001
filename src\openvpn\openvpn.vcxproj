﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{29DF226E-4D4E-440F-ADAF-5829CFD4CA94}</ProjectGuid>
    <RootNamespace>openvpn</RootNamespace>
    <Keyword>Win32Proj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v120</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(SolutionDir)$(Platform)-Output\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(Configuration)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</LinkIncremental>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(SolutionDir)$(Platform)-Output\$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(Configuration)\</IntDir>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(SOURCEBASE);$(SOURCEBASE)/src/compat;$(SOURCEBASE)/include;$(TAP_WINDOWS_HOME)/include;$(OPENSSL_HOME)/include;$(LZO_HOME)/include;$(PKCS11H_HOME)/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;$(CPPFLAGS);%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <UndefinePreprocessorDefinitions>UNICODE</UndefinePreprocessorDefinitions>
    </ClCompile>
    <ResourceCompile>
      <AdditionalIncludeDirectories>$(SOURCEBASE);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>libeay32.lib;ssleay32.lib;lzo2.lib;pkcs11-helper.dll.lib;gdi32.lib;ws2_32.lib;wininet.lib;crypt32.lib;iphlpapi.lib;winmm.lib;Fwpuclnt.lib;Rpcrt4.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(OPENSSL_HOME)/lib;$(LZO_HOME)/lib;$(PKCS11H_HOME)/lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>$(SOURCEBASE);$(SOURCEBASE)/src/compat;$(SOURCEBASE)/include;$(TAP_WINDOWS_HOME)/include;$(OPENSSL_HOME)/include;$(LZO_HOME)/include;$(PKCS11H_HOME)/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;$(CPPFLAGS);%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <UndefinePreprocessorDefinitions>UNICODE</UndefinePreprocessorDefinitions>
    </ClCompile>
    <ResourceCompile>
      <AdditionalIncludeDirectories>$(SOURCEBASE);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Link>
      <AdditionalDependencies>libeay32.lib;ssleay32.lib;lzo2.lib;pkcs11-helper.dll.lib;gdi32.lib;ws2_32.lib;wininet.lib;crypt32.lib;iphlpapi.lib;winmm.lib;Fwpuclnt.lib;Rpcrt4.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(OPENSSL_HOME)/lib;$(LZO_HOME)/lib;$(PKCS11H_HOME)/lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="argv.c" />
    <ClCompile Include="base64.c" />
    <ClCompile Include="block_dns.c" />
    <ClCompile Include="buffer.c" />
    <ClCompile Include="clinat.c" />
    <ClCompile Include="comp-lz4.c" />
    <ClCompile Include="comp.c" />
    <ClCompile Include="compstub.c" />
    <ClCompile Include="console.c" />
    <ClCompile Include="console_builtin.c" />
    <ClCompile Include="crypto.c" />
    <ClCompile Include="crypto_openssl.c" />
    <ClCompile Include="cryptoapi.c" />
    <ClCompile Include="dhcp.c" />
    <ClCompile Include="error.c" />
    <ClCompile Include="event.c" />
    <ClCompile Include="fdmisc.c" />
    <ClCompile Include="forward.c" />
    <ClCompile Include="fragment.c" />
    <ClCompile Include="gremlin.c" />
    <ClCompile Include="helper.c" />
    <ClCompile Include="httpdigest.c" />
    <ClCompile Include="init.c" />
    <ClCompile Include="interval.c" />
    <ClCompile Include="list.c" />
    <ClCompile Include="lladdr.c" />
    <ClCompile Include="lzo.c" />
    <ClCompile Include="manage.c" />
    <ClCompile Include="mbuf.c" />
    <ClCompile Include="misc.c" />
    <ClCompile Include="mroute.c" />
    <ClCompile Include="mss.c" />
    <ClCompile Include="mstats.c" />
    <ClCompile Include="mtcp.c" />
    <ClCompile Include="mtu.c" />
    <ClCompile Include="mudp.c" />
    <ClCompile Include="multi.c" />
    <ClCompile Include="ntlm.c" />
    <ClCompile Include="occ.c" />
    <ClCompile Include="openvpn.c" />
    <ClCompile Include="options.c" />
    <ClCompile Include="otime.c" />
    <ClCompile Include="packet_id.c" />
    <ClCompile Include="perf.c" />
    <ClCompile Include="pf.c" />
    <ClCompile Include="ping.c" />
    <ClCompile Include="pkcs11.c" />
    <ClCompile Include="pkcs11_openssl.c" />
    <ClCompile Include="platform.c" />
    <ClCompile Include="plugin.c" />
    <ClCompile Include="pool.c" />
    <ClCompile Include="proto.c" />
    <ClCompile Include="proxy.c" />
    <ClCompile Include="ps.c" />
    <ClCompile Include="push.c" />
    <ClCompile Include="reliable.c" />
    <ClCompile Include="route.c" />
    <ClCompile Include="schedule.c" />
    <ClCompile Include="session_id.c" />
    <ClCompile Include="shaper.c" />
    <ClCompile Include="sig.c" />
    <ClCompile Include="socket.c" />
    <ClCompile Include="socks.c" />
    <ClCompile Include="ssl.c" />
    <ClCompile Include="ssl_openssl.c" />
    <ClCompile Include="ssl_verify.c" />
    <ClCompile Include="ssl_verify_openssl.c" />
    <ClCompile Include="status.c" />
    <ClCompile Include="tls_crypt.c" />
    <ClCompile Include="tun.c" />
    <ClCompile Include="win32.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="argv.h" />
    <ClInclude Include="base64.h" />
    <ClInclude Include="basic.h" />
    <ClInclude Include="block_dns.h" />
    <ClInclude Include="buffer.h" />
    <ClInclude Include="circ_list.h" />
    <ClInclude Include="clinat.h" />
    <ClInclude Include="common.h" />
    <ClInclude Include="comp-lz4.h" />
    <ClInclude Include="comp.h" />
    <ClInclude Include="compstub.h" />
    <ClInclude Include="console.h" />
    <ClInclude Include="crypto.h" />
    <ClInclude Include="crypto_backend.h" />
    <ClInclude Include="crypto_openssl.h" />
    <ClInclude Include="cryptoapi.h" />
    <ClInclude Include="dhcp.h" />
    <ClInclude Include="errlevel.h" />
    <ClInclude Include="error.h" />
    <ClInclude Include="event.h" />
    <ClInclude Include="fdmisc.h" />
    <ClInclude Include="forward-inline.h" />
    <ClInclude Include="forward.h" />
    <ClInclude Include="fragment.h" />
    <ClInclude Include="gremlin.h" />
    <ClInclude Include="helper.h" />
    <ClInclude Include="httpdigest.h" />
    <ClInclude Include="init.h" />
    <ClInclude Include="integer.h" />
    <ClInclude Include="interval.h" />
    <ClInclude Include="list.h" />
    <ClInclude Include="lladdr.h" />
    <ClInclude Include="lzo.h" />
    <ClInclude Include="manage.h" />
    <ClInclude Include="mbuf.h" />
    <ClInclude Include="memdbg.h" />
    <ClInclude Include="misc.h" />
    <ClInclude Include="mroute.h" />
    <ClInclude Include="mss.h" />
    <ClInclude Include="mstats.h" />
    <ClInclude Include="mtcp.h" />
    <ClInclude Include="mtu.h" />
    <ClInclude Include="mudp.h" />
    <ClInclude Include="multi.h" />
    <ClInclude Include="ntlm.h" />
    <ClInclude Include="occ-inline.h" />
    <ClInclude Include="occ.h" />
    <ClInclude Include="openvpn.h" />
    <ClInclude Include="options.h" />
    <ClInclude Include="otime.h" />
    <ClInclude Include="packet_id.h" />
    <ClInclude Include="perf.h" />
    <ClInclude Include="pf-inline.h" />
    <ClInclude Include="pf.h" />
    <ClInclude Include="ping-inline.h" />
    <ClInclude Include="ping.h" />
    <ClInclude Include="pkcs11.h" />
    <ClInclude Include="pkcs11_backend.h" />
    <ClInclude Include="platform.h" />
    <ClInclude Include="plugin.h" />
    <ClInclude Include="pool.h" />
    <ClInclude Include="proto.h" />
    <ClInclude Include="proxy.h" />
    <ClInclude Include="ps.h" />
    <ClInclude Include="push.h" />
    <ClInclude Include="pushlist.h" />
    <ClInclude Include="reliable.h" />
    <ClInclude Include="route.h" />
    <ClInclude Include="schedule.h" />
    <ClInclude Include="session_id.h" />
    <ClInclude Include="shaper.h" />
    <ClInclude Include="sig.h" />
    <ClInclude Include="socket.h" />
    <ClInclude Include="socks.h" />
    <ClInclude Include="ssl.h" />
    <ClInclude Include="ssl_backend.h" />
    <ClInclude Include="ssl_common.h" />
    <ClInclude Include="ssl_openssl.h" />
    <ClInclude Include="ssl_verify.h" />
    <ClInclude Include="ssl_verify_backend.h" />
    <ClInclude Include="ssl_verify_openssl.h" />
    <ClInclude Include="status.h" />
    <ClInclude Include="syshead.h" />
    <ClInclude Include="tls_crypt.h" />
    <ClInclude Include="tun.h" />
    <ClInclude Include="win32.h" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="openvpn_win32_resources.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\build\msvc\msvc-generate\msvc-generate.vcxproj">
      <Project>{8598c2c8-34c4-47a1-99b0-7c295a890615}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="..\compat\compat.vcxproj">
      <Project>{4b2e2719-e661-45d7-9203-f6f456b22f19}</Project>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
