sudo: required
dist: trusty

os: linux

language: c

compiler:
  - gcc

env:
  global:
    - JOBS=3
    - PREFIX="${HOME}/opt"
    - TAP_WINDOWS_VERSION=9.21.2
    - LZO_VERSION=2.10
    - PKCS11_HELPER_VERSION=1.22
    - MBEDTLS_VERSION="2.5.1"
    - MBEDTLS_CFLAGS="-I${PREFIX}/include"
    - MBEDTLS_LIBS="-L${PREFIX}/lib -lmbedtls -lmbedx509 -lmbedcrypto"
    - OPENSSL_VERSION="1.0.2l"
    - OPENSSL_CFLAGS="-I${PREFIX}/include"
    - OPENSSL_LIBS="-L${PREFIX}/lib -lssl -lcrypto"
    # The next declaration is the encrypted COVERITY_SCAN_TOKEN, created
    #   via the "travis encrypt" command using the project repo's public key
    - secure: "l9mSnEW4LJqjxftH5i1NdDaYfGmQB1mPXnSB3DXnsjzkCWZ+yJLfBemfQ0tx/wS7chBYxqUaUIMT0hw4zJVp/LANFJo2vfh//ymTS6h0uApRY1ofg9Pp1BFcV1laG6/u8pwSZ2EBy/GhCd3DS436oE8sYBRaFM9FU62L/oeQBfJ7r4ID/0eB1b8bqlbD4paty9MHui2P8EZJwR+KAD84prtfpZOcrSMxPh9OUhJxzxUvvVoP4s4+lZ5Kgg1bBQ3yzKGDqe8VOgK2BWCEuezqhMMc8oeKmAe7CUkoz5gsGYH++k3I9XzP9Z4xeJKoQnC/82qi4xkJmlaOxdionej9bHIcjfRt7D8j1J0U+wOj4p8VrDy7yHaxuN2fi0K5MGa/CaXQSrkna8dePniCng+xQ2MY/zxuRX2gA6xPNLUyQLU9LqIug7wj4z84Hk9iWib4L20MoPjeEo+vAUNq8FtjOPxMuHNpv4iGGx6kgJm7RXl5vC5hxfK6MprrnYe2U5Mcd8jpzagKBaKHL3zV2FxX9k0jRO9Mccz7M2WnaV0MQ6zcngzTN4+s0kCjhfGKd2F2ANT2Gkhj3Me36eNHfuE0dBbvYCMh4b3Mgd7b/OuXwQWdJ8PjJ1WHXjSOw5sHw1suaV6cEO2Meyz5j1tOkyOi0M9QF+LFenQ9vLH4sBCww8U="

matrix:
  include:
    - env: SSLLIB="openssl" RUN_COVERITY="1"
      os: linux
      compiler: gcc
    - env: SSLLIB="openssl" OPENSSL_VERSION="1.1.0f"
      os: linux
      compiler: gcc
    - env: SSLLIB="openssl" CFLAGS="-fsanitize=address"
      os: linux
      compiler: clang
    - env: SSLLIB="openssl" OPENSSL_VERSION="1.1.0f"
      os: linux
      compiler: clang
    - env: SSLLIB="mbedtls"
      os: linux
      compiler: gcc
    - env: SSLLIB="mbedtls" CFLAGS="-fsanitize=address"
      os: linux
      compiler: clang
    - env: SSLLIB="openssl"
      os: osx
      osx_image: xcode7.3
      compiler: clang
    - env: SSLLIB="mbedtls"
      os: osx
      osx_image: xcode7.3
      compiler: clang
    - env: SSLLIB="openssl" CHOST=x86_64-w64-mingw32 OPENSSL_VERSION="1.0.1u"
      os: linux
      compiler: ": Win64 build only"
    - env: SSLLIB="openssl" CHOST=x86_64-w64-mingw32 OPENSSL_VERSION="1.1.0h"
      os: linux
      compiler: ": Win64 build only"
    - env: SSLLIB="openssl" CHOST=i686-w64-mingw32
      os: linux
      compiler: ": Win32 build only"
    - env: SSLLIB="openssl" EXTRA_CONFIG="--disable-crypto" EXTRA_SCRIPT="make distcheck"
      os: linux
      compiler: clang
    - env: SSLLIB="openssl" EXTRA_CONFIG="--disable-lzo"
      os: linux
      compiler: clang
    - env: SSLLIB="openssl" EXTRA_CONFIG="--enable-small"
      os: linux
      compiler: clang
  exclude:
    - compiler: gcc

addons:
  apt:
    packages:
      - liblzo2-dev
      - libpam0g-dev
      - liblz4-dev
      - linux-libc-dev
      - man2html

cache:
  directories:
  - download-cache
  - ${HOME}/opt
  - ${HOME}/Library/Caches/Homebrew

before_install:
  - if [ "${TRAVIS_OS_NAME}" = "osx" ]; then brew update     ; fi
  - if [ "${TRAVIS_OS_NAME}" = "osx" ]; then brew install lzo; fi

install:
  - if [ ! -z "${CHOST}" ]; then unset CC; fi
  - .travis/build-deps.sh > build-deps.log 2>&1 || (cat build-deps.log && exit 1)

before_script:
  - .travis/coverity.sh

script:
  - .travis/build-check.sh
