# Perform a TLS loopback test -- client side.
#
# This test performs a TLS negotiation once every 10 seconds,
# and will terminate after 2 minutes.
#
# From the root directory of the OpenVPN distribution,
# after openvpn has been built, run:
#
#  ./openvpn --config sample-config-files/loopback-client  (In one window) 
#  ./openvpn --config sample-config-files/loopback-server  (Simultaneously in another window) 

rport 16000
lport 16001
remote localhost
local localhost
dev null
verb 3
reneg-sec 10
tls-client
remote-cert-tls server
ca sample-keys/ca.crt
key sample-keys/client.key
cert sample-keys/client.crt
tls-auth sample-keys/ta.key 1
ping 1
inactive 120 10000000
