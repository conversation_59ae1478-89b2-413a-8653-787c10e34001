#!/bin/sh

# Copyright (c) 2005-2018 OpenVPN Inc
# Licensed under the GPL version 2

# First version by <PERSON>
# someone at boldandbusted dink com
# http://www.boldandbusted.com/

# PURPOSE: This script automatically removes the /etc/resolv.conf entries previously
# set by the companion script "client.up".

# INSTALL NOTES:
# Place this in /etc/openvpn/client.down
# Then, add the following to your /etc/openvpn/<clientconfig>.conf:
#   client
#   up /etc/openvpn/client.up
#   down /etc/openvpn/client.down
# Next, "chmod a+x /etc/openvpn/client.down"

# USAGE NOTES:
# Note that this script is best served with the companion "client.up"
# script.

# Tested under Debian lenny with OpenVPN 2.1_rc11
# It should work with any UNIX with a POSIX sh, /etc/resolv.conf or resolvconf

# This runs with the context of the OpenVPN UID/GID 
# at the time of execution. This generally means that
# the client "up" script will run fine, but the "down" script
# will require the use of the OpenVPN "down-root" plugin
# which is in the plugins/ directory of the OpenVPN source tree
# The config example above would have to be changed to:
#   client
#   up /etc/openvpn/client.up
#   plugin openvpn-plugin-down-root.so "/etc/openvpn/client.down"

# A horrid work around, from a security perspective,
# is to run OpenVPN as root. THIS IS NOT RECOMMENDED. You have
# been WARNED.
PATH=/bin:/usr/bin:/usr/local/bin:/sbin:/usr/sbin:/usr/local/sbin

if type resolvconf >/dev/null 2>&1; then
  resolvconf -d "${dev}" -f
elif [ -e /etc/resolv.conf.ovpnsave ] ; then
  # cp + rm rather than mv in case it's a symlink
  cp /etc/resolv.conf.ovpnsave /etc/resolv.conf
  rm -f /etc/resolv.conf.ovpnsave
fi

exit 0
