OpenVPN Change Log
Copyright (C) 2002-2018 OpenVPN Inc <<EMAIL>>

2019.02.18 -- Version 2.4.7
Adam Ciarcin?ski (1):
      Fix subnet topology on NetBSD (2.4).

<PERSON> (3):
      add support for %lu in argv_printf and prevent ASSERT
      buffer_list: add functions documentation
      ifconfig-ipv6(-push): allow using hostnames

<PERSON><PERSON> (7):
      Properly free tuntap struct on android when emulating persist-tun
      Add OpenSSL compat definition for RSA_meth_set_sign
      Add support for tls-ciphersuites for TLS 1.3
      Add better support for showing TLS 1.3 ciphersuites in --show-tls
      Use right function to set TLS1.3 restrictions in show-tls
      Add message explaining early TLS client hello failure
      Fallback to password authentication when auth-token fails

<PERSON> (1):
      systemd: extend CapabilityBoundingSet for auth_pam

<PERSON> (1):
      plugin: Export base64 encode and decode functions

<PERSON><PERSON> (3):
      Add %d, %u and %lu tests to test_argv unit tests.
      Fix combination of --dev tap and --topology subnet across multiple platforms.
      Add 'printing of port number' to mroute_addr_print_ex() for v4-mapped v6.

<PERSON><PERSON> (1):
      Minor reliability layer documentation fixes

James Bekkema (1):
      Resolves small IV_GUI_VER typo in the documentation.

Jonathan K. Bullard (1):
      Clarify and expand management interface documentation

Lev Stipakov (5):
      Refactor NCP-negotiable options handling
      init.c: refine functions names and description
      interactive.c: fix usage of potentially uninitialized variable
      options.c: fix broken unary minus usage
      Remove extra token after #endif

Richard van den Berg via Openvpn-devel (1):
      Fix error message when using RHEL init script

Samy Mahmoudi (1):
      man: correct a --redirection-gateway option flag

Selva Nair (7):
      Replace M_DEBUG with D_LOW as the former is too verbose
      Correct the declaration of handle in 'struct openvpn_plugin_args_open_return'
      Bump version of openvpn plugin argument structs to 5
      Move get system directory to a separate function
      Enable dhcp on tap adapter using interactive service
      Pass the hash without the DigestInfo header to NCryptSignHash()
      White-list pull-filter and script-security in interactive service

Simon Rozman (2):
      Add Interactive Service developer documentation
      Detect TAP interfaces with root-enumerated hardware ID

Steffan Karger (7):
      man: add security considerations to --compress section
      mbedtls: print warning if random personalisation fails
      Fix memory leak after sighup
      travis: add OpenSSL 1.1 Windows build
      Fix --disable-crypto build
      Don't print OCC warnings about 'key-method', 'keydir' and 'tls-auth'
      buffer_list_aggregate_separator(): simplify code


2018.04.19 -- Version 2.4.6
David Sommerseth (1):
      management: Warn if TCP port is used without password

Gert Doering (2):
      Correct version in ChangeLog - should be 2.4.5, was mistyped as 2.4.4
      Fix potential double-free() in Interactive Service (CVE-2018-9336)

Gert van Dijk (1):
      manpage: improve description of --status and --status-version

Joost Rijneveld (1):
      Make return code external tls key match docs

Selva Nair (3):
      Delete the IPv6 route to the "connected" network on tun close
      Management: warn about password only when the option is in use
      Avoid overflow in wakeup time computation

Simon Matter (1):
      Add missing #ifdef SSL_OP_NO_TLSv1_1/2

Steffan Karger (1):
      Check for more data in control channel


2018.02.28 -- Version 2.4.5
Antonio Quartulli (4):
      reload HTTP proxy credentials when moving to the next connection profile
      Allow learning iroutes with network made up of all 0s (only if netbits < 8)
      mbedtls: fix typ0 in comment
      manpage: fix simple typ0

Arne Schwabe (2):
      Treat dhcp-option DNS6 and DNS identical
      show the right string for key-direction

Bertrand Bonnefoy-Claudet (1):
      Fix typo in error message: "optione" -> "option"

David Sommerseth (8):
      lz4: Fix confused version check
      lz4: Fix broken builds when pkg-config is not present but system library is
      Remove references to keychain-mcd in Changes.rst
      lz4: Rebase compat-lz4 against upstream v1.7.5
      systemd: Add and ship README.systemd
      Update copyright to include 2018 plus company name change
      man: Add .TQ groff support macro
      man: Reword --management to prefer unix sockets over TCP

Emmanuel Deloget (1):
      OpenSSL: check EVP_PKEY key types before returning the pkey

Gert Doering (2):
      Remove warning on pushed tun-ipv6 option.
      Fix removal of on-link prefix on windows with netsh

Ilya Shipitsin (2):
      travis-ci: add brew cache, remove ccache
      travis-ci: modify openssl build script to support openssl-1.1.0

James Bottomley (1):
      autoconf: Fix engine checks for openssl 1.1

Jeremie Courreges-Anglas (2):
      Cast time_t to long long in order to print it.
      Fix build with LibreSSL

Selva Nair (14):
      Check whether in pull_mode before warning about previous connection blocks
      Avoid illegal memory access when malformed data is read from the pipe
      Fix missing check for return value of malloc'd buffer
      Return NULL if GetAdaptersInfo fails
      Use RSA_meth_free instead of free
      Bring cryptoapi.c upto speed with openssl 1.1
      Add SSL_CTX_get_max_proto_version() not in openssl 1.0
      TLS v1.2 support for cryptoapicert -- RSA only
      Refactor get_interface_metric to return metric and auto flag separately
      Ensure strings read from registry are null-terminated
      Make most registry values optional
      Use lowest metric interface when multiple interfaces match a route
      Adapt to RegGetValue brokenness in Windows 7
      Fix format spec errors in Windows builds

Simon Rozman (11):
      Local functions are not supported in MSVC. Bummer.
      Mixing wide and regular strings in concatenations is not allowed in MSVC.
      RtlIpv6AddressToStringW() and RtlIpv4AddressToStringW() require mstcpip.h
      Simplify iphlpapi.dll API calls
      Fix local #include to use quoted form
      Document ">PASSWORD:Auth-Token" real-time message
      Fix typo in "verb" command examples
      Uniform swprintf() across MinGW and MSVC compilers
      MSVC meta files added to .gitignore list
      openvpnserv: Add support for multi-instances
      Document missing OpenVPN states

Steffan Karger (21):
      make struct key * argument of init_key_ctx const
      buffer_list_aggregate_separator(): add unit tests
      Add --tls-cert-profile option.
      Use P_DATA_V2 for server->client packets too
      Fix memory leak in buffer unit tests
      buffer_list_aggregate_separator(): update list size after aggregating
      buffer_list_aggregate_separator(): don't exceed max_len
      buffer_list_aggregate_separator(): prevent 0-byte malloc
      Fix types around buffer_list_push(_data)
      ssl_openssl: fix compiler warning by removing getbio() wrapper
      travis: use clang's -fsanitize=address to catch more bugs
      Fix --tls-version-min and --tls-version-max for OpenSSL 1.1+
      Add support for TLS 1.3 in --tls-version-{min, max}
      Plug memory leak if push is interrupted
      Fix format errors when cross-compiling for Windows
      Log pre-handshake packet drops using D_MULTI_DROPPED
      Enable stricter compiler warnings by default
      Get rid of ax_check_compile_flag.m4
      mbedtls: don't use API deprecated in mbed 2.7
      Warn if tls-version-max < tls-version-min
      Don't throw fatal errors from create_temp_file()

hashiz (1):
      Fix '--bind ipv6only'


2017.09.25 -- Version 2.4.4
Antonio Quartulli (23):
      crypto: correct typ0 in error message
      use M_ERRNO instead of explicitly printing errno
      don't print errno twice
      ntlm: avoid useless cast
      ntlm: unwrap multiple function calls
      route: improve error message
      management: preserve wait_for_push field when asking for user/pass
      tls-crypt: avoid warnings when --disable-crypto is used
      ntlm: convert binary buffers to uint8_t *
      ntlm: restyle compressed multiple function calls
      ntlm: improve code style and readability
      OpenSSL: remove unreachable call to SSL_CTX_get0_privatekey()
      make function declarations C99 compliant
      remove unused functions
      use NULL instead of 0 when assigning pointers
      add missing static attribute to functions
      ntlm: avoid breaking anti-aliasing rules
      remove the --disable-multi config switch
      rename mroute_extract_addr_ipv4 to mroute_extract_addr_ip
      route: avoid definition of unused variables in certain configurations
      fix a couple of typ0s in comments and strings
      fragment.c: simplify boolean expression
      tcp-server: ensure AF family is propagated to child context

Arne Schwabe (2):
      Set tls-cipher restriction before loading certificates
      Print ec bit details, refuse management-external-key if key is not RSA

Conrad Hoffmann (2):
      Use provided env vars in up/down script.
      Document down-root plugin usage in client.down

David Sommerseth (11):
      doc: The CRL processing is not a deprecated feature
      cleanup: Move write_pid() to where it is being used
      contrib: Remove keychain-mcd code
      cleanup: Move init_random_seed() to where it is being used
      sample-plugins: fix ASN1_STRING_to_UTF8 return value checks
      Highlight deprecated features
      Use consistent version references
      docs: Replace all PolarSSL references to mbed TLS
      systemd: Ensure systemd shuts down OpenVPN in a proper way
      systemd: Enable systemd's auto-restart feature for server profiles
      lz4: Move towards a newer LZ4 API

Emmanuel Deloget (3):
      OpenSSL: remove pre-1.1 function from the OpenSSL compat interface
      OpenSSL: remove EVP_CIPHER_CTX_new() from the compat layer
      OpenSSL: remove EVP_CIPHER_CTX_free() from the compat layer

Gert van Dijk (1):
      Warn that DH config option is only meaningful in a tls-server context

Ilya Shipitsin (3):
      travis-ci: add 3 missing patches from master to release/2.4
      travis-ci: update openssl to 1.0.2l, update mbedtls to 2.5.1
      travis-ci: update pkcs11-helper to 1.22

Richard Bonhomme (1):
      man: Corrections to doc/openvpn.8

Steffan Karger (17):
      Fix typo in extract_x509_extension() debug message
      Move adjust_power_of_2() to integer.h
      Undo cipher push in client options state if cipher is rejected
      Remove strerror_ts()
      Move openvpn_sleep() to manage.c
      fixup: also change missed openvpn_sleep() occurrences
      Always use default keysize for NCP'd ciphers
      Move create_temp_file() out of #ifdef ENABLE_CRYPTO
      Deprecate --keysize
      Deprecate --no-replay
      Move run_up_down() to init.c
      tls-crypt: introduce tls_crypt_kt()
      crypto: create function to initialize encrypt and decrypt key
      Add coverity static analysis to Travis CI config
      tls-crypt: don't leak memory for incorrect tls-crypt messages
      travis: reorder matrix to speed up build
      Fix bounds check in read_key()

Szilárd Pfeiffer (1):
      OpenSSL: Always set SSL_OP_CIPHER_SERVER_PREFERENCE flag

Thomas Veerman via Openvpn-devel (1):
      Fix socks_proxy_port pointing to invalid data


2017.06.21 -- Version 2.4.3
Antonio Quartulli (1):
      Ignore auth-nocache for auth-user-pass if auth-token is pushed

David Sommerseth (3):
      crypto: Enable SHA256 fingerprint checking in --verify-hash
      copyright: Update GPLv2 license texts
      auth-token with auth-nocache fix broke --disable-crypto builds

Emmanuel Deloget (8):
      OpenSSL: don't use direct access to the internal of X509
      OpenSSL: don't use direct access to the internal of EVP_PKEY
      OpenSSL: don't use direct access to the internal of RSA
      OpenSSL: don't use direct access to the internal of DSA
      OpenSSL: force meth->name as non-const when we free() it
      OpenSSL: don't use direct access to the internal of EVP_MD_CTX
      OpenSSL: don't use direct access to the internal of EVP_CIPHER_CTX
      OpenSSL: don't use direct access to the internal of HMAC_CTX

Gert Doering (6):
      Fix NCP behaviour on TLS reconnect.
      Remove erroneous limitation on max number of args for --plugin
      Fix edge case with clients failing to set up cipher on empty PUSH_REPLY.
      Fix potential 1-byte overread in TCP option parsing.
      Fix remotely-triggerable ASSERT() on malformed IPv6 packet.
      Update Changes.rst with relevant info for 2.4.3 release.

Guido Vranken (6):
      refactor my_strupr
      Fix 2 memory leaks in proxy authentication routine
      Fix memory leak in add_option() for option 'connection'
      Ensure option array p[] is always NULL-terminated
      Fix a null-pointer dereference in establish_http_proxy_passthru()
      Prevent two kinds of stack buffer OOB reads and a crash for invalid input data

Jérémie Courrèges-Anglas (2):
      Fix an unaligned access on OpenBSD/sparc64
      Missing include for socket-flags TCP_NODELAY on OpenBSD

Matthias Andree (1):
      Make openvpn-plugin.h self-contained again.

Selva Nair (1):
      Pass correct buffer size to GetModuleFileNameW()

Steffan Karger (11):
      Log the negotiated (NCP) cipher
      Avoid a 1 byte overcopy in x509_get_subject (ssl_verify_openssl.c)
      Skip tls-crypt unit tests if required crypto mode not supported
      openssl: fix overflow check for long --tls-cipher option
      Add a DSA test key/cert pair to sample-keys
      Fix mbedtls fingerprint calculation
      mbedtls: fix --x509-track post-authentication remote DoS (CVE-2017-7522)
      mbedtls: require C-string compatible types for --x509-username-field
      Fix remote-triggerable memory leaks (CVE-2017-7521)
      Restrict --x509-alt-username extension types
      Fix potential double-free in --x509-alt-username (CVE-2017-7521)

Steven McDonald (1):
      Fix gateway detection with OpenBSD routing domains


2017.05.11 -- Version 2.4.2
David Sommerseth (5):
      auth-token: Ensure tokens are always wiped on de-auth
      docs: Fixed man-page warnings discoverd by rpmlint
      Make --cipher/--auth none more explicit on the risks
      plugin: Fix documentation typo for type_mask
      plugin: Export secure_memzero() to plug-ins

Hristo Venev (1):
      Fix extract_x509_field_ssl for external objects, v2

Selva Nair (1):
      In auth-pam plugin clear the password after use

Steffan Karger (10):
      cleanup: merge packet_id_alloc_outgoing() into packet_id_write()
      Don't run packet_id unit tests for --disable-crypto builds
      Fix Changes.rst layout
      Fix memory leak in x509_verify_cert_ku()
      mbedtls: correctly check return value in pkcs11_certificate_dn()
      Restore pre-NCP frame parameters for new sessions
      Always clear username/password from memory on error
      Document tls-crypt security considerations in man page
      Don't assert out on receiving too-large control packets (CVE-2017-7478)
      Drop packets instead of assert out if packet id rolls over (CVE-2017-7479)

ValdikSS (1):
      Set a low interface metric for tap adapter when block-outside-dns is in use

2017.03.21 -- Version 2.4.1
Antonio Quartulli (4):
      attempt to add IPv6 route even when no IPv6 address was configured
      fix redirect-gateway behaviour when an IPv4 default route does not exist
      CRL: use time_t instead of struct timespec to store last mtime
      ignore remote-random-hostname if a numeric host is provided

Christian Hesse (7):
      man: fix formatting for alternative option
      systemd: Use automake tools to install unit files
      systemd: Do not race on RuntimeDirectory
      systemd: Add more security feature for systemd units
      Clean up plugin path handling
      plugin: Remove GNUism in openvpn-plugin.h generation
      fix typo in notification message

David Sommerseth (6):
      management: >REMOTE operation would overwrite ce change indicator
      management: Remove a redundant #ifdef block
      git: Merge .gitignore files into a single file
      systemd: Move the READY=1 signalling to an earlier point
      plugin: Improve the handling of default plug-in directory
      cleanup: Remove faulty env processing functions

Emmanuel Deloget (8):
      OpenSSL: check for the SSL reason, not the full error
      OpenSSL: don't use direct access to the internal of X509_STORE_CTX
      OpenSSL: don't use direct access to the internal of SSL_CTX
      OpenSSL: don't use direct access to the internal of X509_STORE
      OpenSSL: don't use direct access to the internal of X509_OBJECT
      OpenSSL: don't use direct access to the internal of RSA_METHOD
      OpenSSL: SSLeay symbols are no longer available in OpenSSL 1.1
      OpenSSL: use EVP_CipherInit_ex() instead of EVP_CipherInit()

Eric Thorpe (1):
      Fix Building Using MSVC

Gert Doering (4):
      Add openssl_compat.h to openvpn_SOURCES
      Fix '--dev null'
      Fix installation of IPv6 host route to VPN server when using iservice.
      Make ENABLE_OCC no longer depend on !ENABLE_SMALL

Gisle Vanem (1):
      Crash in options.c

Ilya Shipitsin (2):
      Resolve several travis-ci issues
      travis-ci: remove unused files

Olivier Wahrenberger (1):
      Fix building with LibreSSL 2.5.1 by cleaning a hack.

Selva Nair (4):
      Fix push options digest update
      Always release dhcp address in close_tun() on Windows.
      Add a check for -Wl, --wrap support in linker
      Fix user's group membership check in interactive service to work with domains

Simon Matter (1):
      Fix segfault when using crypto lib without AES-256-CTR or SHA256

Steffan Karger (8):
      More broadly enforce Allman style and braces-around-conditionals
      Use SHA256 for the internal digest, instead of MD5
      OpenSSL: 1.1 fallout - fix configure on old autoconf
      Fix types in WIN32 socket_listen_accept()
      Remove duplicate X509 env variables
      Fix non-C99-compliant builds: don't use const size_t as array length
      Deprecate --ns-cert-type
      Be less picky about keyUsage extensions


2016.12.26 -- Version 2.4.0
David Sommerseth (5):
      dev-tools: Added script for updating copyright years in files
      Update copyrights
      docs: Further enhance the documentation related to SWEET32
      man: Remove references to no longer present IV_RGI6 peer-info
      build: Ensure Changes.rst is shipped and installed as a doc file

Gert Doering (1):
      Remove IV_RGI6=1 peer-info signalling.

Steffan Karger (3):
      Document that RSA_SIGN can also request TLS 1.2 signatures
      man: encourage user to read on about --tls-crypt
      Textual fixes for Changes.rst


2016.12.16 -- Version 2.4_rc2
David Sommerseth (9):
      Fix wrong configure.ac parsing of --enable-async-push
      Changes: Further improve systemd unit file updates
      systemd: Intermediate --chroot fix with the new sd_notify() implementation
      Further enhance async-push feature description
      Changes.rst: Mainatiner update on C99
      dev-tools: Add reformat-all.sh for code style unification
      The Great Reformatting - first phase
      Merge 'reformatting' branch into master
      auth-gen-token: Hardening memory cleanup on auth-token failuers

Gert Doering (1):
      Refactor setting close-on-exec for socket FDs

Lev Stipakov (2):
      Arm inotify only in server mode
      Add "async push" feature to Changes.rst

Magnus Kroken (1):
      mbedtls: include correct net/net_sockets header according to version

Selva Nair (2):
      Correctly state the default dhcp server address in man page
      Unhide a line in man page by fixing a typo

Steffan Karger (4):
      Fix (and cleanup) crypto flags in combination with NCP
      Deprecate --no-iv
      man: mention that --ecdh-curve does not work on mbed TLS builds
      Don't reopen tun if cipher changes


2016.12.01 -- Version 2.4_rc1
Antonio Quartulli (1):
      reload CRL only if file was modified

Christian Hesse (3):
      update year in copyright message
      Use systemd service manager notification
      Refuse to daemonize when running from systemd

Gert Doering (1):
      Fix windows path in Changes.rst

Samuli Seppänen (1):
      Mention that OpenVPN 2.4 requires Windows Vista or higher

Selva Nair (4):
      Map restart signals from event loop to SIGTERM during exit-notification wait
      When parsing '--setenv opt xx ..' make sure a third parameter is present
      Force 'def1' method when --redirect-gateway is done through service
      Do not restart dns client service as a part of --register-dns processing

Steffan Karger (4):
      tls_process: don't set variable that's never read
      Unconditionally enable TLS_AGGREGATE_ACK
      Clean up format_hex_ex()
      Introduce and use secure_memzero() to erase secrets


2016.11.24 -- Version 2.4_beta2
Arne Schwabe (5):
      Document that tls-crypt also supports inline
      Fix warning that RAND_bytes is undeclared
      Remove compat-stdbool.h.
      Fix various compiler warnings
      Handle DNS6 option on Android

David Sommerseth (2):
      Changes.rst: Fixing wrong formatting
      Document the --auth-token option

Gert Doering (2):
      Remove remaining traces of compat-stdbool.h
      Stub implementation of "--dhcp-option DNS6 <v6addr>"

Selva Nair (3):
      Do not set ipv6 address if '--ip-win32 manual' is used
      Handle --dhcp-option DNS6 on Windows using netsh
      Set IPv6 DNS servers using interactive service

Steffan Karger (6):
      multi_process_float: revert part of c14c4a9e
      --tls-crypt fixes
      Change cmocka remote to use https in stead of git protocol
      generate_key_expansion: make assumption explicit, use C99 features
      Poor man's NCP for non-NCP peers
      Refactor data channel key generation API


2016.11.17 -- Version 2.4_beta1
Arne Schwabe (1):
      Make Changes.rst nicer for 2.4 release

David Sommerseth (16):
      Update .mailmap to unify and clean up odd names and e-mail addresses
      cleanup: Remove NOP code sections in ssl.c:tls_process()
      Remove last rest of INSTALL-win32.txt references
      auth-gen-token: Add --auth-gen-token option
      auth-gen-token: Generate an auth-token per client
      auth-gen-token: Push generated auth-tokens to the client
      auth-gen-token: Authenticate generated auth-tokens when client re-authenticates
      Fix builds with --disable-crypto
      man: Improve the --keepalive section
      console: Fix compiler warning
      systemd: Improve the systemd unit files
      tun: Fix compiler warnings
      file checks: Merge warn_if_group_others_accessible() into check_file_access()
      tun: Fix weird commit error causing a double assignment
      options: Remove --tls-remote
      Remove unused variable in argv_printf_arglist()

Gert Doering (10):
      openvpn version line: remove [IPv6], add [AEAD] if available
      clean up *sig_info handling in link_socket_init_phase2()
      check c->c2.link_socket before calling do_init_route_ipv6_list()
      Check previously-unchecked buf_alloc_write() call in crypto self-test.
      Fix potential division by zero in shaper_reset()
      Repair topology subnet on FreeBSD 11
      Repair topology subnet on OpenBSD
      Add in_port_t check to configure.ac
      Fix compilation on MinGW with -std=c99
      Replace WIN32 by _WIN32

Heiko Hund (4):
      put argv_* functions into own file, add unit tests
      Remove unused and unecessary argv interfaces
      remove unused system_str from struct argv
      Factor out %sc handling from argv_printf()

Lev Stipakov (1):
      Drop recursively routed packets

Samuli Seppänen (6):
      Remove INSTALL-win32.txt that is now hosted in openvpn-build
      Fix update_t_client_ips.sh for out of tree builds
      Make sure that all relevant files under test go to release tarballs
      Allow passing extra arguments to fping/fping6 in t_client.rc
      Prevent generation of duplicate EXPECT_IFCONFIG entries
      Fix a logic problem in handling of --up scripts in t_client.sh

Selva Nair (2):
      Support --block-outside-dns on multiple tunnels
      Unbreak windows build

Steffan Karger (19):
      Fix use-after-free bug in prepare_push_reply()
      Remove verbose msg() from send_push_reply()
      Limit --reneg-bytes to 64MB when using small block ciphers
      Add a revoked cert to the sample keys
      Fix --tls-version-max in mbed TLS builds
      Don't deference type-punned pointers
      Fix builds on compilers without anonymous union support
      Refactor static/tls-auth key loading
      Add missing includes in error.h
      Make argv unit tests obey {MBEDTLS, OPENSSL}_{LIBS, CFLAGS}
      Move private file access checks to options_postprocess_filechecks()
      Deprecate key-method 1
      Refactor CRL handling
      Remove unneeded check for extra_certs_file_inline
      Fix missing return value checks in multi_process_float()
      Restore pre-NCP cipher options on SIGUSR1
      Remove unused variables from do_init_crypto_static()
      Add control channel encryption (--tls-crypt)
      Add --tls-crypt unit tests


2016.10.19 -- Version 2.4_alpha2

David Sommerseth (1):
      Update .mailmap to unify and clean up odd names and e-mail addresses

Steffan Karger (1):
      Fix use-after-free bug in prepare_push_reply()


2016.10.17 -- Version 2.4_alpha1

Adriaan de Jong (2):
      Fixed a bug where PolarSSL gave an error when using an inline file tag.
      Fix --show-pkcs11-ids (Bug #239)

Alexander Pyhalov (1):
      Default gateway can't be determined on illumos/Solaris platforms

Alon Bar-Lev (1):
      pkcs11: use generic evp key instead of rsa

Andris Kalnozols (3):
      Fix some typos in the man page.
      Do not upcase x509-username-field for mixed-case arguments.
      extract_x509_extension(): hide status message during normal operation.

Arne Schwabe (100):
      Document man agent-external-key
      Options parsing demands unnecessary configuration if PKCS11 is used
      Error message if max-routes used incorrectly
      Properly require --key even if defined(MANAGMENT_EXTERNAL_KEY)
      Remove dnsflags_to_socktype, it is not used anywhere
      Fix the proto is used inconsistently warning
      Remove dead code path and putenv functionality
      Remove unused function xor
      Move static prototype definition from header into c file
      Remove unused function no_tap_ifconfig
      Add the client id (CID) to the output of the status command
      Print client id only if compiled with man agent support. Otherwise print an empty string.
      Allow routes to be set before opening tun, similar to ifconfig before opening tun
      Add ability to send/receive file descriptors via management interface
      Android platform specific changes.
      Emulate persist-tun on Android
      Document the Android implementation in OpenVPN
      Only print script warnings when a script is used. Remove stray mention of script-security system.
      Fix #ifdefs for P2MP_SERVER
      Move settings of user script into set_user_script function
      Move checking of script file access into set_user_script
      Fix another #ifdef/#if P2MP_SERVER
      PATCHv3 Remove unused variables or put them to the defines they are being used in
      Add support of utun devices under Mac OS X
      Add support to ignore specific options.
      Add a note what setenv opt does for OpenVPN < 2.3.3
      Implement custom HTTP header for http-proxy, and always send user-agent:
      Add reporting of UI version to basic push-peer-info set.
      Change the type of all ports in openvpn to const char* and let getaddrinfo resolve the port together with the hostname.
      Fix compile error in ssl_openssl introduced by polar external-management patch
      Simplify print_sockaddr_ex function, merge duplicate ipv4/ipv6 logic.
      Split the PROTO_UDP_xx options into AF_INET/AF_INET6 and PROTO_TCP/PROTO_UDP part.
      Fix two instances of asserting AF_INET
      Fix assertion when SIGUSR1 is received while getaddrinfo is successful
      Split link_socket_init_phase1 and link_socket_init_phase2 into smaller more managable/readable functions. No functional changes
      Change proto_remote() function to return a constant string
      Remove the ip-remote-hint option.
      change the type of 'remote' to addrinfo*, and rename to 'remote_list'.
      When resolving fails print the error message from socket layer
      Implement dual stack client support for OpenVPN
      Move ASSERT so external-key with OpenSSL works again
      Implement listing on IPv4/IPv6 dual socket on all platform
      Add warning for using connection block variables after connection blocks
      Update IPv6 related readme files
      Introduce safety check for http proxy options
      Fix warning for max-routes: do not quit when parsing an old configuration. Format the message to be more like the other deprecated options
      Fix connecting to localhost on Android
      Move the initialization of the environment to the top so c2.es is initialized
      Workaround broken Android 4.4 VpnService API for persist-tun mode
      Implement an easy parsable log output that allows access to flags of the log message
      Introduce an option to resolve dns names in advance for --remote, --local and --http-proxy
      Fix for server selecting address family
      Don't show the connection profile store in options->ce if there is a connection_list defined.
      Add gateway and device to android control messages
      Clean up of socket code.
      Fix assert when using port-share
      Work around Solaris getaddrinfo() returing ai_protocol=0
      Fix man page and OSCP script: tls_serial_{n} is decimal
      Remove ENABLE_BUFFER_LIST
      Fix server routes not working in topology subnet with --server [v3]
      Always enable http-proxy and socks-proxy
      Remove deprecated --max-routes option from manual
      Add documentation for PERSIST_TUN_ACTION (Android specific)
      Remove possibility of using --tls-auth with non OpenVPN Static key files
      Remove unused function sock_addr_set
      Document the default for tls-cipher.
      Report missing end-tags of inline files as errors
      Fix commit e473b7c if an inline file happens to have a line break exactly at buffer limit
      Show extra-certs in current parameters, fix clang warning and logic error in preresolve
      Remove unused function h_errno_msg
      Add support for requesting the fd again to rebind to the next interface.
      Don't redirect the gateway on Android even if requested
      Fix loglevel of protect socket message
      Extend network-change command to allow reprotecting on the same network (for short connection losses)
      Use pseudo gw as default gw on Android as a workaround for not being able to read /proc/net/route
      Remove #ifdefs for client nat support.
      Do not install a host route for the VPN on Android
      Fix commit c67acea173dc9ee37220f5b9ff14ede081181992
      Do not set the buffer size by default but rely on the operation system default.
      Start Changes.rst that lists changes in 2.4.0
      Remove --enable-password-save option
      Reflect enable-password-save change in documentation
      Also remove second instance of enable-password-save in the man page
      Detect config lines that are too long and give a warning/error
      Implement the compression V2 data format for stub and lz4.
      Fix assert when comp is called with unknown algorithm, always call comp init method
      Ignore stamp-h2 we generate during build process
      Implement inlining of crl files
      Complete push-peer-info documentation and allow IV_PLAT_VER for other platforms than Windows if the client UI supplies it.
      Remove http-proxy-timeout, socks timeout and set default of server-poll-timeout to 120s
      Add documentation for http-proxy-user-pass option
      Remove http-proxy-retry and socks-proxy-retry.
      Update android documentation to match source code
      Use AES ciphers in our sample configuration files and add a few modern 2.4 examples
      Fix ENABLE_CRYPTO_OPENSSL set to YES even with --disable-crypto set
      Prefer RECVDSTADDR to PKTINFO for IPv4 in OS X since it actually works (unlike PKTINFO)
      Incorporate the Debian typo fixes where appropriate and make show_opt default message clearer
      Enable TCP non-linear packet ID
      Change the hold command to communicate the time that OpenVPN would wait to the UI.
      Remove tun-ipv6 Option. Instead assume that IPv6 is always supported.

Boris Lytochkin (1):
      Log serial number of revoked certificate

Christian Hesse (1):
      fix build with automake 1.13(.1)

Christian Niessner (1):
      Fix corner case in NTLM authentication (trac #172)

Christos Trochalakis (1):
      Adjust server-ipv6 documentation

Cristian Rodriguez (1):
      Use SSL_MODE_RELEASE_BUFFERS if available

Daniel Hahler (1):
      options: fix option check for "plugin"

Daniel Kubec (4):
      Added support for TLS Keying Material Exporters [RFC-5705]
      Added document for TLS Keying Material Exporters [RFC-5705]
      sample-plugin: TLS Keying Material Exporter [RFC-5705] demonstration plug-in
      Fix buffer size parameter for exported keying material.

David Sommerseth (44):
      Make git ignore some more files
      Remove the support for using system() when executing external programs or scripts
      Fix double-free issue in pf_destroy_context()
      Reset the version.m4 version for the master branch
      Avoid recursion in virtual_output_callback_func()
      The get_default_gateway() function uses warn() instead of msg()
      Improve the git revision tracking
      man page: Update man page about the tls_digest_{n} environment variable
      Remove the --disable-eurephia configure option
      plugin: Extend the plug-in v3 API to identify the SSL implementation used
      autoconf: Fix typo
      t_client.sh: Check for fping/fping6 availability
      t_client.sh: Write errors to stderr and document requirements
      t_client.sh: Add prepare/cleanup possibilties for each test case
      Fix file checks when --chroot is being used
      Adjusted autotools files to build more cleanly on newer autoconf/automake versions
      Improve error reporting on file access to --client-config-dir and --ccd-exclusive
      Don't let openvpn_popen() keep zombies around
      Don't try to use systemd-ask-password if it is not available
      Clean up the pipe closing in openvpn_popen()
      Add systemd unit file for OpenVPN
      systemd: Use systemd functions to consider systemd availability
      systemd: Reworked the systemd unit file to handle server and client configs better
      autotools: Fix wrong ./configure help screen default values
      down-root plugin: Replaced system() calls with execve()
      down-root: Improve error messages
      plugin, down-root: Fix compiler warnings
      sockets: Remove the limitation of --tcp-nodelay to be server-only
      plugins, down-root: Code style clean-up
      Provide compile time OpenVPN version information to plug-ins
      Provide OpenVPN runtime version information to plug-ins
      Avoid partial authentication state when using --disabled in CCD configs
      Only build and run cmocka unit tests if its submodule is initialized
      Another fix related to unit test framework
      Remove NOP function and callers
      Revert "Drop recursively routed packets"
      Fix client connection instant timeout
      t_client.sh: Make OpenVPN write PID file to avoid various sudo issues
      t_client.sh: Add support for Kerberos/ksu
      t_client.sh: Improve detection if the OpenVPN process did start during tests
      Rework the user input interface to make it more modular
      Re-implement the systemd support using the new query user API
      systemd: Do not mask usernames when querying for it via systemd-ask-password
      Move memcmp_constant_time() to crypto.h

David Woodhouse (2):
      pkcs11: Load p11-kit-proxy.so module by default
      Make 'provider' option to --show-pkcs11-ids optional where p11-kit is present

Davide Brini (2):
      Provide more accurate warning message
      Document authfile for socks server

Dmitrij Tejblum (1):
      Fix is_ipv6 in case of tap interface.

Dorian Harmans (1):
      Add CHACHA20-POLY1305 ciphersuite IANA name translations.

Felix Janda (1):
      Use OPENVPN_ETH_P_* so that <netinet/if_ether.h> is unecessary

Fish (1):
      Add lz4 support to MSVC.

Gert Doering (110):
      Implement --mssfix handling for IPv6 packets.
      Fix option inconsistency warnings about "proto" and "tun-ipv6"
      Fix parameter type for IP_TOS setsockopt on non-Linux systems.
      Fix client crash on double PUSH_REPLY.
      Update README.IPv6 to match what is in 2.3.0
      Repair "tcp server queue overflow" brokenness, more <stdbool.h> fallout.
      Permit pool size of /64.../112 for ifconfig-ipv6-pool
      Add MIN() compatibility macro
      Fix directly connected routes for "topology subnet" on Solaris.
      Print "Virtual IPv6 Address" on management interface queries [v4]
      Use constrain_int() instead of MIN()+syshead.c compat definition - v2.
      Fix NULL-pointer crash in route_list_add_vpn_gateway().
      Fix usage of 'compression ...' from global config.
      Make push-peer-info visible in "normal" per-instance environment.
      Fix problem with UDP tunneling due to mishandled pktinfo structures.
      Improve documentation and help text for --route-ipv6.
      Fix argument type warning introduced by http extra proxy header patch.
      Fix IPv6 examples in t_client.rc-sample
      Fix slow memory drain on each client renegotiation.
      t_client.sh: ignore fields from "ip -6 route show" output that distort results.
      Fix IPv6_V6ONLY logic.
      Implement LZ4 compression.
      Provide LZ4 sources in src/compat/ and use if no system lz4 library found.
      Document "lz4" argument to "compress" config option.
      Make code and documentation for --remote-random-hostname consistent.
      Reduce IV_OPENVPN_GUI_VERSION= to IV_GUI_VER=
      remove some 'unused variable' warnings
      Cleanup ir6->netbits handling.
      Document issue with --chroot, /dev/urandom and PolarSSL.
      Rename 'struct route' to 'struct route_ipv4'
      Replace copied structure elements with including <net/route.h>
      Add "test-driver" and "compile" to .gitignore
      Fix crash when using --inetd.
      IPv6 address/route delete fix for Win8
      Add SSL library version reporting.
      Minor t_client.sh cleanups
      Repair --multihome on FreeBSD for IPv4 sockets.
      Rewrite manpage section about --multihome
      More IPv6-related updates to the openvpn man page.
      Conditionalize calls to print_default_gateway on !ENABLE_SMALL
      Merge get_default_gateway() implementation for all 4+1 BSD variants.
      Drop incoming fe80:: packets silently now.
      Recognize AIX, define TARGET_AIX
      Add tap driver initialization and ifconfig for AIX.
      implement adding/deleting routes on AIX, for IPv4 and IPv6
      Make t_client.sh work on AIX.
      Fix t_lpback.sh platform-dependent failures
      Call init script helpers with explicit path (./)
      Fix windows build on older mingw versions.
      New approach to handle peer-id related changes to link-mtu.
      Print remote IPv4 address on a dual-stack v6 socket in IPv4 format
      Fix incorrect use of get_ipv6_addr() for iroute options.
      Remove count_netmask_bits(), convert users to use netmask_to_netbits2()
      Fix leftover 'if (false) ;' statements
      Print helpful error message on --mktun/--rmtun if not available.
      explain effect of --topology subnet on --ifconfig
      Add note about file permissions and --crl-verify to manpage.
      repair --dev null breakage caused by db950be85d37
      assume res_init() is always there.
      Correct note about DNS randomization in openvpn.8
      Disallow usage of --server-poll-timeout in --secret key mode.
      slightly enhance documentation about --cipher
      Enforce "serial-tests" behaviour for tests/Makefile
      Revert "Enforce "serial-tests" behaviour for tests/Makefile"
      On signal reception, return EAI_SYSTEM from openvpn_getaddrinfo().
      Use configure.ac hack to apply serial_test AM option only if supported.
      Use EAI_AGAIN instead of EAI_SYSTEM for openvpn_getaddrinfo().
      Move res_init() call to inner openvpn_getaddrinfo() loop
      Fix FreeBSD ifconfig for topology subnet tunnels.
      Produce a meaningful error message if --daemon gets in the way of asking for passwords.
      Document --daemon changes and consequences (--askpass, --auth-nocache).
      Fix build on OpenSolaris (non-gmake)
      Un-break --auth-user-pass on windows
      refactor struct route_ipv6, bring in line with struct route_ipv4 again
      refactor struct route_ipv6_list, bring in line with struct route_list again
      Add route_ipv6_gateway* data structures for rgi6 support.
      Create basic infrastructure for IPv6 default gateway handling / redirection.
      Make client delay less before sending PUSH_REQUEST
      get_default_gateway_ipv6(): Linux / Netlink implementation.
      Implement handling of overlapping IPv6 routes with IPv6 remote VPN server address
      Implement '--redirect-gateway ipv6'
      get_default_gateway_ipv6(): *BSD / MacOS / Solaris PF_ROUTE implementation
      Fix IPv6 host routes to LAN gateway on OpenSolaris
      Replace unaligned 16bit access to TCP MSS value with bytewise access
      Repair test_local_addr() on WIN32
      Add custom check for inet_pton()/inet_ntop() on MinGW/WIN32
      get_default_gateway_ipv6(): Win32 implementation using GetBestRoute2()
      Remove support for snappy compression.
      Fix info.af == AF_UNSPEC case for server with --mtu-disc
      Fix FreeBSD-specific mishandling of gc arena pointer in create_arbitrary_remote()
      remove unused gc_arena in FreeBSD close_tun()
      Un-break compilation on *BSD
      Fix isatty() check for good.
      Fix openserv/validate.o linking issues on mingw.
      Fix library order in -lmbedtls test.
      Implement push-remove option to selectively remove pushed options.
      Upgrade bundled compat-lz4 to upstream release r131.
      Change --enable-pedantic to use -std=c99 and not -ansi (C90).
      Fix problems with NCP and --inetd.
      Do not abort t_client run if OpenVPN instance does not start.
      Fix IP_PKTINFO related compilation failure on NetBSD 7.0
      Show compile-time variant for --multihome in --version output.
      Fix win32 building with C99 mode
      Fix t_client runs on OpenSolaris
      make t_client robust against sudoers misconfiguration
      add POSTINIT_CMD_suf to t_client.sh and sample config
      Fix --multihome for IPv6 on 64bit BSD systems.
      Enable -D_SVR4_2 for compilation on Solaris
      Revert "Enable -D_SVR4_2 for compilation on Solaris"
      Enable -D_XPG4_2 for compilation on Solaris

Guy Yur (1):
      Fix --redirect-private in --dev tap mode.

Heikki Hannikainen (1):
      Always load intermediate certificates from a PKCS#12 file

Heiko Hund (20):
      Fix display of plugin hook types
      Support UTF-8 --client-config-dir
      close more file descriptors on exec
      Ignore UTF-8 byte order mark
      reintroduce --no-name-remapping option
      make --tls-remote compatible with pre 2.3 configs
      add new option for X.509 name verification
      Support non-ASCII TAP adapter names on Windows
      Support non-ASCII characters in Windows tmp path
      make sure sa_family_t is defined
      convert struct signal_info element
      grow route lists dynamically
      fix route struct name
      refine assertion to allow other modes than CBC
      Fix compilation on Windows
      fix warnings on Windows
      extend management interface command "state"
      put virtual IPv6 addresses into env
      interactive service v3
      Windows: do_ifconfig() after open_tun()

Holger Kummert (1):
      Del ipv6 addr on close of linux tun interface

Hubert Kario (2):
      ocsp_check - signature verification and cert staus results are separate
      ocsp_check - double check if ocsp didn't report any errors in execution

Ilya Shipitsin (3):
      initial travis-ci support
      skip t_lpback.sh and t_cltsrv.sh if openvpn configured --disable-crypto
      enable "--disable-crypto" build configuration for travis

Ivo Manca (1):
      Plug memory leak in mbedTLS backend

James Bekkema (1):
      Fix socket-flag/TCP_NODELAY on Mac OS X

James Geboski (1):
      Fix --askpass not allowing for password input via stdin

James Yonan (14):
      Added support for the Snappy compression algorithm
      Always push basic set of peer info values to server.
      TLS version negotiation
      Added "setenv opt" directive prefix.  If present, and if the directive that follows is recognized, it will be processed as if the "setenv opt" prefix was absent.  If present and if the directive that follows is not recognized, the directive will be ignored rather than cause a fatal error.
      MSVC fixes
      Set SSL_OP_NO_TICKET flag in SSL context for OpenSSL builds, to disable TLS stateless session resumption.
      Use native strtoull() with MSVC 2013.
      Define PATH_SEPARATOR for MSVC builds.
      Fixed some compile issues with show_library_versions()
      Added flags parameter to format_hex_ex.
      Extended x509-track for OpenSSL to report SHA1 fingerprint.
      Fixed port-share bug with DoS potential
      Added directive to specify HTTP proxy credentials in config.
      Bind to local socket before dropping privileges

Jan Just Keijser (5):
      man page patch for missing options
      make 'explicit-exit-notify' pullable again
      include ifconfig_ environment variables in --up-restart env set
      Author: Jan Just Keijser <<EMAIL>>
      Make certificate expiry warning patch (091edd8e299686) work on OpenSSL 1.0.1 and earlier.

Jann Horn (1):
      Remove quadratic complexity from openvpn_base64_decode()

Jeffrey Cutter (1):
      Update contrib/pull-resolv-conf/client.up for no DOMAIN

Jens Neuhalfen (6):
      Make intent of utun device name validation clear
      Fix buffer overflow by user supplied data
      ignore the local config file t_client.rc in git
      Prevent integration test timeout bc. of sudo
      Add unit testing support via cmocka
      Add a test for auth-pam searchandreplace

Jens Wagner (1):
      Fix spurious ignoring of pushed config options (trac#349).

Jesse Glick (1):
      Allow use of NetBeans without saving nbproject/ directory.

Joachim Schipper (5):
      doc/management-notes.txt: fix typo
      Fix typo in ./configure message
      Refactor tls_ctx_use_external_private_key()
      --management-external-key for PolarSSL
      external_pkcs1_sign: Support non-RSA_SIG_RAW hash_ids

Jonathan K. Bullard (3):
      Fix mismatch of fprintf format specifier and argument type
      Fix null pointer dereference in options.c
      Fail if options have extra parameters [v2]

Josh Cepek (7):
      Fix parameter listing in non-debug builds at verb 4
      (updated) [PATCH] Warn when using verb levels >=7 without debug
      Fix proto tcp6 for server & non-P2MP modes
      Fix Windows script execution when called from script hooks
      Correct error text when no Windows TAP device is present
      Require a 1.2.x PolarSSL version
      Push an IPv6 CIDR mask used by the server, not the pool's size

Julien Muchembled (1):
      Fix --mtu-disc option with IPv6 transport

Kenneth Rose (1):
      Fix v3 plugins to support returning values back to OpenVPN.

Klee Dienes (1):
      tls_ctx_load_ca: Improve certificate error messages

Leon Klingele (1):
      Add link to bug tracker

Leonardo Basilio (1):
      Correctly report TCP connection timeout on windows.

Lev Stipakov (26):
      Peer-id patch v7
      Add the peer-id to the output of the status command
      Prevent memory drain for long lasting floating sessions
      Disallow lameduck's float to an address taken by another client
      Fix NULL dereferencing
      Fix mssfix default value in connection_list context
      This fixes MSVS 2013 compilation.
      Continuation of MSVS fixes
      Fast recovery when host is in unreachable network
      Fix compilation error with --disable-crypto
      Send push reply right after async auth complete
      Fix compilation with --disable-server
      Refine float logging
      Generate openvpn-plugin.h for MSVC build
      Replace variable length array with malloc
      Use adapter index instead of name for windows IPv6 interface config
      Notify clients about server's exit/restart
      Use adapter index for add/delete_route_ipv6
      Pass adapter index to up/down scripts
      Detecting and logging Windows versions
      Report Windows bitness
      Fix "implicit declaration" compiler warning
      Drop recursively routed packets
      Support for disabled peer-id
      Exclude peer-id from pulled options digest
      Use separate list for per-client push options

Lukasz Kutyla (1):
      Fix privilege drop if first connection attempt fails

Matthias Andree (1):
      Enable TCP_NODELAY configuration on FreeBSD.

Max Muster (1):
      Remove duplicate cipher entries from TLS translation table.

Michael McConville (1):
      Fix undefined signed shift overflow

Michal Ludvig (1):
      Support for username-only auth file.

Mike Gilbert (2):
      Add configure check for the path to systemd-ask-password
      Include systemd units in the source tarball (make dist)

Niels Ole Salscheider (1):
      Fix build with libressl

Peter Sagerson (1):
      Fix configure interaction with static OpenSSL libraries

Philipp Hagemeister (2):
      Add topology in sample server configuration file
      Implement on-link route adding for iproute2

Phillip Smith (1):
      Use bob.example.com and alice.example.com to improve clarity of documentation

Robert Fischer (1):
      Updated manpage for --rport and --lport

Samuel Thibault (1):
      Ensure that client-connect files are always deleted

Samuli Seppänen (15):
      Removed ChangeLog.IPv6
      Added cross-compilation information INSTALL-win32.txt
      Updated README
      Cleaned up and updated INSTALL
      Fix to --shaper documentation on the man-page
      Properly escape dashes on the man-page
      Improve documentation in --script-security section of the man-page
      Add CONTRIBUTING.rst
      Update CONTRIBUTING.rst to allow GitHub PRs for code review purposes
      Clarify the fact that build instructions in README are for release tarballs
      Mention tap-windows6 in INSTALL file
      Use an up-to-date easy-rsa URL on the man-page
      Clarify which Windows versions require which TUN/TAP driver
      Deprecate the automatic part of openvpnserv.exe in favor of openvpnserv2.exe
      Automatically cache expected IPs for t_client.sh on the first run

Selva Nair (26):
      Fix termination when windows suspends/sleeps
      Do not hard-code windows systemroot in env_block
      Handle ctrl-C and ctrl-break events on Windows
      Unbreak read username password from management
      Restrict options/configs for startup through interactive service
      Send stdout and stderr of OpenVPN started by interactive service to NUL
      Handle localized Administrators group name in windows
      Fix interactive service ignoring stop command if openvpn is running
      Use appropriate buffer size for WideCharToMultiByte output in interactive.c
      Refactor and move the block-outside-dns code to a new file (block_dns.[ch])
      Add support for block-outside-dns through the interactive service
      Ensure input read using systemd-ask-password is null terminated
      Support reading the challenge-response from console
      Make error non-fatal while deleting address using netsh
      Add support for register-dns through interactive service
      Fix handling of out of memory error in interactive service
      Fix the comparison of pull options hash on restart
      Set WFP engine handle to NULL in win_wfp_uninit()
      Make block-outside-dns work with persist-tun
      Add an option to filter options received from server
      Ignore SIGUSR1/SIGHUP during exit notification
      Fix management-external-cert option parsing error
      Return process id of openvpn from interactive service to client
      Exponentially back off on repeated connect retries
      Promptly close the netcmd_semaphore handle after use
      Avoid format specifier %zu for Windows compatibility

Steffan Karger (180):
      PolarSSL-1.2 support
      Improve PolarSSL key_state_read_{cipher, plain}text messages
      Improve verify_callback messages
      Config compatibility patch. Added translate_cipher_name.
      Switch to IANA names for TLS ciphers.
      Fixed autoconf script to properly detect missing pkcs11 with polarssl.
      Use constant time memcmp when comparing HMACs in openvpn_decrypt.
      Fixed tls-cipher translation bug in openssl-build
      Fixed usage of stale define USE_SSL to ENABLE_SSL
      Do not pass struct tls_session* as void* in key_state_ssl_init().
      Require polarssl >= 1.2.10 for polarssl-builds, which fixes CVE-2013-5915.
      Also update TLSv1_method() calls in support code to SSLv23_method() calls.
      Update TLSv1 error messages to SSLv23 to reflect changes from commit 4b67f98
      If --tls-cipher is supplied, make --show-tls parse the list.
      Remove OpenSSL tmp_rsa_callback. Removes support for ephemeral RSA in TLS.
      Make tls_ctx_restrict_ciphers accept NULL as char *cipher_list.
      Disable export ciphers by default for OpenSSL builds.
      Fix compiler warning for unused result of write()
      Remove unused variables from ssl_verify_polarssl.c's x509_get_serial()
      Fix compiler warnings in ssl_polarssl.c
      Bump minimum OpenSSL version to 0.9.8
      Add openssl-specific common cipher list names to ssl.c.
      Disable unsupported TLS cipher modes by default, cleans --show-tls output.
      configure.ac: check for SSL_OP_NO_TICKET flag in OpenSSL
      configure.ac: use CPPFLAGS for SSL_OP_NO_TICKET check
      Upgrade to PolarSSL 1.3
      Improve error reporting during key/cert loading with PolarSSL.
      Update openvpn-plugin.h for PolarSSL 1.3.
      Add support for elliptic curve diffie-hellmann key exchange (ECDH)
      Add an elliptic curve testing cert chain to the sample keys
      Change signedness of hash in x509_get_sha1_hash(), fixes compiler warning.
      Fix OCSP_check.sh to also use decimal for stdout verification.
      Make serial env exporting consistent amongst OpenSSL and PolarSSL builds.
      Fix build system to accept non-system crypto library locations for plugins.
      Remove function without effect (cipher_ok() always returned true).
      Remove unneeded wrapper functions in crypto_openssl.c
      Remove unneeded defines (were needed for pre-0.9.7 OpenSSL).
      Fix merge error in a6c573d, the ssl ctx is now abstracted.
      Use generic openvpn_x509_cert_t in ssl_verify_polarssl.c
      Fix ssl.c, ssl_verify_* includes
      Move #include "ssl_verify.h" from ssl.h to the source files that need it.
      Remove dependency on manage.h from ssl_verify.h
      Remove unused variable 'proxy' from socket_restart_pause()
      Add (default disabled) --enable-werror option to configure
      Fix --disable-ssl builds, were broken by cleanup in 63dc03d.
      configure.ac: fix SSL_OP_NO_TICKET check
      Fix bug that incorrectly refuses oid representation eku's in polar builds
      Update README.polarssl
      cleanup: remove #if 0'ed function initiate_untrusted_session() from ssl.c.
      Rename ALLOW_NON_CBC_CIPHERS to ENABLE_OFB_CFB_MODE, and add to configure.
      Add proper check for crypto modes (CBC or OFB/CFB)
      Improve --show-ciphers to show if a cipher can be used in static key mode
      Extend t_lpback tests to test all ciphers reported by --show-ciphers
      Don't issue warning for 'translate to self' tls-ciphers
      Don't exit daemon if opening or parsing the CRL fails.
      Define dummy SSL_OP_NO_TICKET flag if not present in OpenSSL.
      Fix typo in cipher_kt_mode_{cbc, ofb_cfb}() doxygen.
      Fix some unintialized variable warnings
      Fix clang warning in options.c
      Fix compiler warnings in ssl_polarssl.c.
      Fix regression with password protected private keys (polarssl)
      Remove unused variables from ssl_verify_openssl.c extract_x509_extension()
      Fix assertion error when using --cipher none
      Add --tls-version-max
      Modernize sample keys and sample configs
      Drop too-short control channel packets instead of asserting out.
      Really fix '--cipher none' regression
      Update doxygen (a bit)
      Set tls-version-max to 1.1 if cryptoapicert is used
      openssl: add crypto_msg(), to easily log openssl errors
      openssl: add more descriptive message for 'no shared cipher' error
      Remove ENABLE_SSL define (and --disable-ssl configure option)
      openssl: use crypto_msg(), get rid of openssl-specific code in error.c
      Add option to disable Diffie Hellman key exchange by setting '--dh none'
      Account for peer-id in frame size calculation
      Disable SSL compression
      Use tls-auth in sample config files
      Fix frame size calculation for non-CBC modes.
      Get rid of old OpenSSL workarounds.
      polarssl: make sure to always null-terminate the cn
      Allow for CN/username of 64 characters (fixes off-by-one)
      Change float log message to include common name, if available.
      Remove unneeded parameter 'first_time' from possibly_become_daemon()
      Remove size limit for files inlined in config
      polarssl: remove code duplication in key_state_write_plaintext{, _const}()
      Improve --tls-cipher and --show-tls man page description
      polarssl: disable 1/n-1 record splitting
      cleanup: remove md5 helper functions
      Re-read auth-user-pass file on (re)connect if required
      Clarify --capath option in manpage
      Call daemon() before initializing crypto library
      write pid file immediately after daemonizing
      Increase control channel packet size for faster handshakes
      Make __func__ work with Visual Studio too
      fix regression: query password before becoming daemon
      Fix using management interface to get passwords.
      reintroduce md5_digest wrapper struct to fix gcc warnings
      Fix out-of-tree builds; openvpn-plugin.h should be in AC_CONFIG_HEADERS
      Fix overflow check in openvpn_decrypt()
      Replace strdup() calls for string_alloc() calls
      Check return value of ms_error_text()
      polarssl: add easy logging for PolarSSL errors
      polarssl: Improve PolarSSL logging
      openssl: be less verbose about cipher translation errors
      hardening: add insurance to exit on a failed ASSERT()
      Fix memory leak in auth-pam plugin
      openssl: remove usage of OPENSSL_malloc() from show_available_curves
      polarssl: fix --client-cert-not-required
      polarssl: add --verify-client-cert optional support
      Fix (potential) memory leak in init_route_list()
      Add macro to ensure we exit on fatal errors
      polarssl: also allocate PKCS#11 certificate object on demand
      polarssl: don't use deprecated functions anymore
      polarssl: require >= 1.3.8
      Fix memory leak in add_option() by simplifying get_ipv6_addr
      remove nonsense const specifier in nonfatal() return value
      openssl: properly check return value of RAND_bytes()
      Fix rand_bytes return value checking
      Fix openssl builds with custom-built library: specify most-dependent first
      Support duplicate x509 field values in environment
      Warn user if their certificate has expired
      Disable certificate notBefore/notAfter sanity check on OpenSSL < 1.0.2
      Make assert_failed() print the failed condition
      cleanup: get rid of httpdigest.c type warnings
      Fix regression in setups without a client certificate
      polarssl: actually use polarssl debug logging
      polarssl: optimize polar_ok() for non-errors
      Update manpage: OpenSSL might also need /dev/urandom inside chroot
      polarssl: use wrappers to access md_info_t member functions
      polarssl: remove now redundant 128-bit blowfish key override
      socks.c: fix check on get_user_pass() return value(s)
      configure.ac: simplify crypto library configuration
      configure.ac: fix polarssl autodetection
      Allow NULL argument in cipher_ctx_get_cipher_kt()
      Remove reuse of key_type during init of data channel auth and tls-auth
      Move crypto_options into key_state and stop using context in SSL-mode.
      Move key_ctx_bi into crypto_options
      Move packet_id into crypto_options
      Change openvpn_encrypt() to append to work buffer only
      Create separate function for replay check
      Add AEAD cipher support (GCM)
      Add cipher name translation for OpenSSL.
      Add preliminary server-side support for negotiable crypto parameters
      Minor AEAD patch cleanup
      Clean up get_tls_handhake_key()
      Fix OCSP_check.sh
      Make AEAD modes work with OpenSSL 1.0.1-1.0.1c
      hardening: add safe FD_SET() wrapper openvpn_fd_set()
      Only include aead encrypt/decrypt functions if AEAD modes are supported
      Fix potential null-pointer dereference
      Fix memory leak in argv_extract_cmd_name()
      Replace MSG_TEST() macro for static inline msg_test()
      fixup: change init_key_type() param name in declaration too
      Further restrict default cipher list
      PolarSSL x509_get_sha1_hash now returns correct SHA1 fingerprint.
      Implemented x509-track for PolarSSL.
      Migrate to mbed TLS 2.x
      Rename files with 'polarssl' in the name to 'mbedtls'
      configure.ac: link to all mbed TLS libs during library detection
      mbedtls: check that private key and certificate match on start
      mbedtls: improve error reporting in tls verify callback
      Remove trailing newline from verify callback error messages
      Don't limit max incoming message size based on c2->frame
      cleanup: remove alloc_buffers argument from multi_top_init()
      mbedtls: don't set debug threshold if compiled without MBEDTLS_DEBUG_C
      Add client-side support for cipher negotiation
      Add options to restrict cipher negotiation
      Add server-side support for cipher negotiation
      Allow ncp-disable and ncp-ciphers to be specified in ccd files
      Fix '--cipher none --cipher' crash
      Discourage using 64-bit block ciphers
      Fix unittests for out-of-source builds
      Fix --mssfix when using NCP
      Drop gnu89/c89 support, switch to c99
      cleanup: remove code duplication in msg_test()
      Add SHA256 fingerprint support
      Make sure options->ciphername and options->authname are always defined
      Update cipher-related man page text
      Fix duplicate PUSH_REPLY options
      Check --ncp-ciphers list on startup

TDivine (1):
      Fix "code=995" bug with windows NDIS6 tap driver.

Tamas TEVESZ (1):
      Add support for client-cert-not-required for PolarSSL.

Thomas Veerman (2):
      Fix "." in description of utun.
      Update expiry date in management event loop

ValdikSS (4):
      Add Windows DNS Leak fix using WFP ('block-outside-dns')
      Clarify mssfix documentation
      Clarify --block-outside-dns documentation
      Update --block-outside-dns to work on Windows Vista

Vasily Kulikov (1):
      Mac OS X Keychain management client

Yawning Angel (1):
      Fix SOCKSv5 method selection

Yegor Yefremov (3):
      socket: remove duplicate expression
      polarssl: fix unreachable code
      cert_data: fix memory leak

janjust (1):
      Fix "White space before end tags can break the config parser"

kangsterizer (1):
      Fix typo in sample build script to use LDFLAGS

svimik (1):
      Fix segfault when enabling pf plug-ins


2012.09.12 -- Version 2.3_beta1
Arne Schwabe (7):
      Fixes error: --key fails with EXTERNAL_PRIVATE_KEY: No such file or directory if --management-external-key is used
      Merge almost identical create_socket_tcp and create_socket_tcp6
      Document the inlining of files in openvpn and document key-direction
      Merge getaddr_multi and getaddr6 into one function
      Document --management-client and --management-signal a bit better
      Document that keep alive will double the second value in server mode and give a short explanation why the value is chosen.
      Add checks for external-key-managements

David Sommerseth (1):
      Fix reconnect issues when --push and UDP is used on the server

Gert Doering (4):
      Reduce --version string detail about IPv6 to just "[IPv6]".
      Put actual OpenVPN command line on top of corresponding log file.
      Keep pre-existing tun/tap devices around on *BSD
      make "ipv6 ifconfig" on linux compatible with busybox ifconfig

Heiko Hund (6):
      fix regression with --http-proxy[-*] options
      add x_msg_va() log function
      add API for plug-ins to write to openvpn log
      remove stale _openssl_get_subject() prototype
      remove unused flag SSLF_NO_NAME_REMAPPING
      Add --compat-names option

2012.07.20 -- Version 2.3_alpha3
Arne Schwabe (1):
      Fix compiling with --disable-management

Gert Doering (1):
      Repair "tap server" mode brokenness caused by <stdbool.h> fallout

Heiko Hund (4):
      make non-blocking connect work on Windows
      don't treat socket related errors special anymore
      remove unused show_connection_list debug function
      add option --management-query-proxy

2012.06.29 -- Version 2.3_alpha2
Adriaan de Jong (11):
      Fixed off-by-one in serial length calculation
      Migrated x509_get_subject to use of the garbage collector
      Migrated x509_get_serial to use the garbage collector
      Migrated x509_get_sha1_hash to use the garbage collector
      Ensure sys/un.h autoconf detection includes sys/socket.h
      Added support for new PolarSSL 1.1 RNG
      Added a configuration option to enable prediction resistance in the PolarSSL random number generator.
      Use POLARSSL_CFLAGS instead of POLARSSL_CRYPTO_CFLAGS in configure.ac
      Removed support for PolarSSL < 1.1
      Updated README.polarssl with build system changes.
      Removed stray "Fox-IT hardening" string.

Alon Bar-Lev (94):
      build: version should not contain '-'
      package: rpm: strip should be handled by package management
      cleanup: options.c: remove redundant include
      cleanup: remove C++ warnings
      cleanup: win32.c: wrong printf format
      cleanup: remove redundant ';'
      cleanup: crypto_openssl.c: remove support for pre-openssl-0.9.6
      cleanup: tun.c: fix incorrect option in message (ip-win32)
      cleanup: memcmp.c: remove unused source
      fixup: init.c: add missing conditional for ENABLE_CLIENT_CR
      build: correct place to alter WINVER is at build system
      Update .gitignore
      build: handle printf style format in mingw
      build: rename plugin directory to plugins
      build: plugins: properly use CC, CFLAGS and LDFLAGS
      build: we need the sample.ovpn in future
      Remove install-win32
      Remove easy-rsa
      Remove tap-win32
      cleanup: rename tap-windows function from win32 to win
      build: remove windows specific build system
      build: split acinclude.m4 into m4/*
      build: m4/ax_varargs.m4: cleanup
      build: m4/ax_emptyarray.m4: cleanup
      build: m4/ax_socklen_t.m4: cleanup
      build: autotools: first pass of trivial autotools changes
      build: autoconf: remove OPENVPN_ADD_LIBS useless macro
      build: remove awk and non-standard autoconf output processing
      build: standard directory layout
      build: add libtool + windows resources for executables
      build: autoconf: commands as environment
      build: libdl usage
      build: properly detect and use socket libs
      build: autoconf: minor cleanups
      build: proper selinux detection and usage
      build: distribute pkg.m4
      build: proper pkcs11-helper detection and usage
      build: properly process lzo-stub
      build: proper lzo detection and usage
      build: proper crypto detection and usage
      build: autoconf: update defaults for options
      build: win-msvc: msbuild format
      build: move out config.h include from syshead
      build: split out compat
      build: move gettimeofday() emulation to compat
      build: move daemon() emulation into compat
      build: move inet_ntop(), inet_pton() emulation into compat
      cleanup: move console related function into its own module
      build: move wrappers into platform module
      build: windows: install version.sh to allow installer read version
      build: distribute samples in windows
      build: use tap-windows.h as external dependency
      build: ax_varargs.m4: fixups
      build: autoconf: misc sockets fixups
      build: enable lzo by default
      build: windows: set vendor to openvpn project + cleanups
      build: assume dlfcn is available on all supported platforms
      build: openbsd: detect netinet/ip.h correctly
      build: tap: search for tap header
      build: msvc: upgrade to Visual Studio 2010 + fixups
      Enable pedantic in windows compilation
      cleanup: flags should not be bool
      cleanup: avoid using ~0 - generic
      cleanup: avoid using ~0 - ipv6
      cleanup: avoid using ~0 - netmask
      cleanup: avoid using ~0 - windows
      cleanup: gc usage
      build: fix some statement left from conversion
      build: properly detect netinet/ip.h structs
      build: properly detect TUNSETPERSIST
      cleanup: plugin: support C++ plugin
      cleanup: remove C++ comments
      cleanup: add .gitattributes to control eol style explicitly
      crash: packet_id_debug_print: sl may be null
      build: use stdbool.h if available
      build: fix typo in --enable-save-password
      build: windows: convert resources to UTF-8
      build: check minimum polarssl version
      cleanup: update .gitignore
      cleanup: spec: make space/tab consistent
      build: spec: we support openssl >= 0.9.7
      build: insall README* document using build system
      build: detect sys/wait.h required for *bsd
      build: add git revision to --version output if build from git repository
      build: cleanup: yet another forgotten brackets
      build: update INSTALL to recent changes
      build: support platforms that does not need explicit tun headers
      build: do not support <polarssl-1.1.0
      build: add --with-special-build to provide special build string
      cleanup: pkcs11.c: resolve wanings
      build: integrate plugins build into core build
      build: plugins: set defaults based on platform
      cleanup: windows: convert argv (UCS-2 to UTF-8) at earliest
      build: msvc: chdir with change drive to script location

Arne Schwabe (7):
      Add the query to the error message.
      Explain that route-nopull also causes the client to ignore dhcp options.
      Add the name of the context where option is not allowed to the error message.
      Only use tmpdir if tmp_dir is really used.
      Completely remove ancient IANA port warning.
      Remove ENABLE_INLINE_FILES conditionals
      Remove ENABLE_CONNECTIONS ifdefs

David Sommerseth (5):
      Clean-up: Presume that Linux is always IPv6 capable at build time
      Simplify check_cmd_access() function
      Change version to indicate the master branch is not a version
      Some filesystems don't like ':', which is a path 'make dist' would use
      Remove two unused functions

Frank de Brabander (1):
      Fix reported compile issues on OSX 10.6.8

Gert Doering (10):
      repair t_client.sh test after build system revolution
      t_client.sh iproute2 script fixes
      t_client.sh - fix for iproute2, print summary line
      Implement search for "first free" tun/tap device on Solaris
      cleanup and redefine metric handling for IPv6 routes
      remove "*option" element in "struct route_ipv6"
      Remove warning about explicit support for IPv6 support not provided MacOS X
      Add missing pieces to IPv6 route gateway handling.
      Update TODO.IPv6 list
      Remove #include "config.h" from ssl_polarssl.h

Heiko Hund (3):
      remove wrapper code for Windows CryptoAPI function
      fix warnings in event.c when building for win32-64
      remove the --auto-proxy option from openvpn

Igor Novgorodov (1):
      Remove calls to OpenSSL when building with --disable-ssl

Jonathan K. Bullard (2):
      Fix file access checks on commands
      Clarified the docs and help screen about what a 'cmd' is

Samuli Seppänen (1):
      Added notes about upgrading from 2.3-alpha1 and earlier to INSTALL-win32.txt

2012.02.21 -- Version 2.3-alpha1
Adriaan de Jong (127):
      Added Doxygen doxyfile
      Changed configure to accept --with-ssl-type=openssl
      Refactored to rand_bytes for OpenSSL-independency
      Refactored OpenSSL-specific constants
      Refactored maximum cipher and hmac length constants
      Refactored show_available_* functions
      Refactored SSL_clear_error()
      Refactored crypto initialisation functions
      Refactored DES key manipulation functions
      Refactored NTLM DES key generation
      Refactored message digest type functions
      Refactored message digest functions
      Refactored HMAC functions
      Refactored cipher key types
      Refactored cipher functions
      Added PRNG doxygen
      Refactored: Moved crypto.h inline functions to end of file
      Removed stale OpenSSL defines from crypto.h
      Added a check for Openssl or PolarSSL defines
      Refactored: Added stubs for new files
      Refactored SSL initialisation functions
      Refactored TLS_PRF to new hmac and md primitives
      Refactored tls_show_available_ciphers
      Refactored get_highest_preference_tls_cipher
      Refactored root SSL context initialisation
      Refactored new external key code
      Refactored DH paramater loading
      Refactored root TLS option settings
      Refactored PKCS#12 key loading
      Refactored PKCS#11 loading
      Refactored windows cert loading
      Refactored load certificate functions
      Refactored private key loading code
      Refactored external key loading from management
      Refactored CA and extra certs code
      Refactored cipher restriction code
      Refactored tls_options, key_state, and key_source data structures
      Refactored initalisation of key_states
      Refactored key_state free code
      Refactored print_details
      Refactored key_state read code (including bio_read())
      Refactored key_state write functions
      Refactored: Moved BIO debug functions to OpenSSL backend
      Refactored: removed ks and ks_lame macro for clarity
      Refactored: moved write_empty_string function back
      Refactored Doxygen for tls_multi functions
      Migrated data structures needed by verification functions to ssl_common.h
      Refactored client_config_dir_exclusive function
      Refactored certificate hash lock checks
      Refactored common name locking functions
      Refactored username and password authentication code
      Add some extra comments
      Refactored: split verify_callback into two parts
      Added function to extract and verify the subject from a certificate
      Added function to verify and extract the username
      Refactored: removed global x509_username_field
      Refactored: separated environment setup during verification
      Refactored: Netscape certificate type verification
      Refactored key usage verification code
      Refactored EKU verification
      Refactored tls-remote checking
      Refactored tls-verify-plugin code
      Refactored tls-verify script code
      Refactored CRL checks
      Minor cleanup in verify_cert:
      Refactored: Moved verify_cert to ssl_verify
      Cleaned up ssl.h
      Refactored: made M_SSL dependent on USE_OPENSSL
      Refactored: renamed X509 functions from verify_*
      Separated OpenSSL-specific parts of the PKCS#11 driver
      Modified base64 code in preparation for PolarSSL merge
      Final cleanup before PolarSSL addition:
      Refactored X509 track feature to be contained within the openssl backend
      Added PolarSSL support:
      Fixed a missing include in ssl_backend.h
      Fixed a bug in the hash generation in ssl_verify_openssl.c
      Added SHA_DIGEST_SIZE definition
      Changed PolarSSL crypto backend to support v0.99-pre5
      Updated ssl_polarssl.c to work with 0.99-pre5
      Fixed a compilation warning for size_t key sizes
      Added a warning that the PolarSSL library does not support pkcs12 files.
      Added warning that --capath is not available with PolarSSL
      Disable CryptoAPI when not using OpenSSL, and document that fact.
      Removed support for management external keys in PolarSSL
      Removed stray X509_free from ssl.c
      Refactored (and disabled for PolarSSL) support for writing external cert files in scripts
      Added an extra define to allow building without PKCS#11
      Added SSL library to title string
      Disabled X.509 track and username selection for PolarSSL
      Hardening: periodically reset the PRNG's nonce value
      Fixes for the plugin system:
      Further improvements to plugin support:
      Fixed an unintentional change in the options calculated key size.
      Moved print messages back to generic crypto.c from cipher backends
      Moved HMAC prints back to main crypto module
      Added back checks for ks->authenticated in verify_user_pass
      Moved gc_new and gc_free to begin end of function
      Fixed a bug in the return value of ssl_verify when pre_verify failed
      Unified verification function return values:
      Removed a stray Fox-IT tag
      Fixed a typo: print the subject instead of the serial for verification errors
      Made SSL_CIPHER const in print_details, to fix warning
      Moved to PolarSSL 1.0.0:
      Added missing #ifdef to allow --disable-managent to work again
      Fixed disabling crypto and SSL
      Got rid of a few magic numbers in ntlm.c
      Removed obsolete des_cblock and des_keyschedule
      Further removal of des_old.h based calls
      Fixed missing comma in plugin.h
      Moved prng_uninit out of crypto_uninit_lib
      Moved CryptoAPI header include to the ssl_openssl.c
      Reordered functions to ensure warning-free Windows build
      Added options to switch between OpenSSL and PolarSSL and PKCS11...
      Moved from strsep to strtok, for Windows compatibility
      Minor cleanup to enable warning-free Windows build:
      Fixed a typo when initialising cryptoapi certs
      Minor code cleanup: cleaned up error handling in verify_cert.
      Moved out of memory prototype to error.h, as the definition is in error.c
      Removed support for calling gc_malloc with a NULL gc_arena struct

      (The follwing patches from Adriaan was mistakenly merged with
       the wrong commit author in the git tree)
      Doxygen: Added data channel crypto docs
      Added control channel crypto docs
      Added compression docs
      Added reliability layer documentation
      Added memory management documentation
      Added data channel fragmentation docs
      Added main/control docs
      Moved doxygen-specific files to a separate directory

Byron Ellacott (1):
      autoconf fixes for building on OSX

David Sommerseth (50):
      Provide 'dev_type' environment variable to plug-ins and script hooks
      Define the new openvpn_plugin_{open,func}_v3() API
      Implement the core v3 plug-in function calls.
      Extend the v3 plug-in API to send over X509 certificates
      Added a simple plug-in demonstrating the v3 plug-in API.
      Separate the general plug-in version constant and v3 plug-in structs version
      Use a version-less version identifier on the master branch
      Fix the --client-cert-not-required feature
      Change the default --tmp-dir path to a more suitable path
      Improve the mysprintf() issue in openvpnserv.c
      Add a simple comment regarding openvpn_snprintf() is duplicated
      Merge branch 'feat_ipv6_transport'
      Merge branch 'feat_ipv6_payload'
      Merge branch 'svn-branch-2.1' into merge
      Solved hidden merge conflicts between master and svn-branch-2.1
      Fix const declarations in plug-in v3 structs
      Merge remote-tracking branch 'cron2/feat_ipv6_payload_2.3'
      Don't define ENABLE_PUSH_PEER_INFO if SSL is not available
      Fix compiling issues with pkcs11 when --disable-management is configured
      Remove support for Linux 2.2 configuration fallback
      Revert "Add new openssl.cnf to easy-rsa/Windows"
      Merge remote branch SVN 2.1 into the git tree
      Merge branch 'svn-merger'
      Fix Microsoft Visual Studio incompatibility in plugin.c
      Fixed compile issues on FreeBSD and Solaris
      Fix PolarSSL and --pkcs12 option issues
      Fix FreeBSD/OpenBSD/NetBSD compiler warnings in get_default_gateway()
      Make '--win-sys env' default
      Do some file/directory tests before really starting openvpn
      Fix bug after removing Linux 2.2 support
      Don't look for 'stdin' file when using --auth-user-pass
      Fix compiling with --disable-crypto and/or --disable-ssl
      Fix a couple of issues in openvpn_execve()
      Move away from openvpn_basename() over to platform provided basename()
      Enable access() when building in Visual Studio
      New Windows build fixes
      Fix compilation errors on Linux platforms without SO_MARK
      autotools ./configure don't like compat.h
      Fix pool logging when IPv6 is not enabled
      Don't check for file presence on inline files
      Add --route-pre-down/OPENVPN_PLUGIN_ROUTE_PREDOWN script/plug-in hook
      Enhance the error handling in _openssl_get_subject()
      Fix assert() situations where gc_malloc() is called without a gc_arena object
      Fix compile issues when plug-ins are disabled.
      Remove --show-gateway if debug info is not enabled (--disable-debug)
      Fix compile issues with status.c
      Connection entry {tun,link}_mtu_defined not set correctly
      Makefile.am referenced a now non-existing config-win32.h
      Makefile.am was missing ssl_common.h
      Revamp check_file_access() checks in stdin scenarios

Davide Guerri (1):
      New feauture: Add --stale-routes-check

Frank de Brabander (1):
      Fixed wrong return type of cipher_kt_mode

Frederic Crozat (1):
      Add support to forward console query to systemd

Gert Doering (45):
      Add more detailed explanation regarding the function of "--rdns-internal"
      Enable IPv6 Payload in OpenVPN p2mp tun server mode.  20100104-1 release.
      remove NOTES file from commit - private scribbling
      NetBSD fixes - on 4.0 and up, use multi-af mode.
      new feature: "ifconfig-ipv6-push" (from ccd/ config)
      add some TODOs to TODO.IPv6
      undo accidential duplication of existing "--iroute" line in the help text
      basic documentation of IPv6 related options and their syntax
      Enable IPv6 Payload in OpenVPN p2mp tun server mode.
      remove NOTES file from commit - private scribbling
      env_block(): if PATH is not set, add standard PATH setting to env
      add IPv6 route add / route delete code for windows (using "netsh")
      - Win32 IPv6 ifconfig support, using "netsh" calls
      drop "book ipv6" from open_tun() and tuncfg() prototypes
      document recent changes and open TODOs, adapt --version info, tag release
      Win32: set next-hop for IPv6 routes according to TUN/TAP mode
      when deleting a route on win32, also add gateway address
      WIN32: if IPv6 requested in TUN mode, check if TUN/TAP driver < 9.7
      revert unconditionally-enabling of setenv_es() logging
      implement IPv6 ifconfig + route setup/deletion on OpenBSD
      full "VPN client connect" test framework for OpenVPN t_client.rc-sample
      renamed t_client.sh to t_client.sh.in
      2.2-beta3 has a signed TAP driver with the IPv6 code - test for 9.8
      correct URL for "more information about IPv6 patch is *here*"
      bugfix for linux/iproute2: IPv6 ifconfig code block was not called for "dev tun"+"topology subnet"
      bump IPv6 version number (openvpn --version) to 20100922-1
      Implement "ipv6 ifconfig" for TAP interfaces on Solaris interfaces
      rebased to 2.2RC2 (beta 2.2 branch)
      Windows IPv6 cleanup - properly remove IPv6 routes and interface config
      For all accesses to "struct route_list * rl", check first that rl is non-NULL
      Replace 32-bit-based add_in6_addr() implementation by an 8-bit based one
      Platform cleanup for NetBSD
      Move block for "stale-routes-check" config inside #ifdef P2MP_SERVER block
      add missing break between "case IPv4" and "case IPv6"
      bump tap driver version from 9.8 to 9.9
      log error message and exit for "win32, tun mode, tap driver version 9.8"
      work around inet_ntop/inet_pton problems for MSVC builds on WinXP
      Fix build-up of duplicate IPv6 routes on reconnect.
      Fix list-overrun checks in copy_route_[ipv6_]option_list()
      add "print test titles" and "use sudo" functionality to t_client.rc
      Platform cleanup for FreeBSD
      Implement IPv6 interface config with non-/64 prefix lengths.
      Fix RUN_SUDO functionality for t_client.sh
      Document IPv6-related environment variables.
      Platform cleanup for OpenBSD

Gisle Vanem (1):
      Avoid re-defining uint32_t when using mingw compiler

Gustavo Zacarias (1):
      Fix compile issues when using --enable-small and --disable-ssl/--disable-crypto

Heiko Hund (16):
      add .gitignore to official repository
      remove function is_proto_tcp()
      remove legacy code to query IE proxy information
      lowercase include header name in syshead.h
      define IN6_ARE_ADDR_EQUAL macro for WIN32
      add --mark option to set SO_MARK sockopt
      Windows UTF-8 input/output
      UTF-8 X.509 distinguished names
      set Windows environment variables as UCS-2
      handle Windows unicode paths
      replace check for TARGET_WIN32 with WIN32
      do not use mode_t on Windows
      use the underscore version of stat on Windows
      make MSVC link against shell32 as well
      move variable declaration to top of function
      define access mode flag X_OK as 0 on Windows

Igor Novgorodov (1):
      The code blocks enabled by ENABLE_CLIENT_CR depends on management

James Yonan (57):
      Added "management-external-key" option.
      Minor addition of logging info before and after execution of Windows net commands.
      Misc fixes to r6708.
      Added --x509-track option.
      * added --management-up-down option to allow management interface to be notified of tunnel up/down events.
      Fixed minor compile issue triggered on builds where MANAGEMENT_DEF_AUTH is not enabled.
      Implemented get_default_gateway_mac_addr for Mac OS X
      Fixes to r6925.
      Properly handle certificate serial numbers > 32 bits.
      Added "client-nat" option for stateless, one-to-one NAT on the client side.
      Renamed branch to reflect that it is no longer beta.
      env_filter_match now includes the serial number of all certs
      Fixed issue where a client might receive multiple push replies from a server
      Fixed bug introduced in r7031 that might cause this error message:
      Extended "client-kill" management interface command (server-side)
      Client will now try to reconnect if no push reply received within handshake-window seconds.
      Version 2.1.3n
      Fixed compiling issues when using --disable-crypto
      Added "management-external-key" option.
      Misc fixes to r6708.
      win/sign.py now accepts an optional tap-dir argument.
      Added "auth-token" client directive
      Added ./configure --enable-osxipconfig option for Mac OS X
      Added more packet ID debug info at debug level 3 for debugging false positive packet replays.
      Fixed bug that incorrectly placed stricter TCP packet replay rules on UDP sessions
      Fixed bug in port-share that could cause port share process to crash
      For Mac OSX, when DARWIN_USE_IPCONFIG is defined, retry ipconfig command on failure
      Version 2.1.3t
      Revert r7092 and r7151, i.e. remove --enable-osxipconfig configure option.
      Added 'dir' flag to "crl-verify" (see man page for info).
      Added new "extra-certs" and "verify-hash" options
      Fixed compile issues on Windows.
      Added --enable-lzo-stub configure option to build an OpenVPN client without LZO
      Added optional journal directory argument to "port-share" directive
      Reduce log verbosity at level 3, with a focus on removing excessive log verbosity generated by port-share activity.
      env_filter_match now includes the serial number of all certs in chain
      Added support for static challenge/response protocol.
      r7316 fixes.
      Added redirect-gateway block-local flag, with support for Linux, Mac OS X
      Extended x509-track to allow SHA1 certificate hash to be extracted
      Added "management-query-remote" directive (client) to allow the management interface to override the "remote" directive.
      Version 2.1.5.
      Fixed MSVC compile error related to r7408.
      Redact "echo" directive strings from log, since these strings (going forward) could conceivably contain security-sensitive data.
      Modified sanitize_control_message to remove redacted data from control string rather than blotting it out with "_" chars.
      Changed CC_PRINT character class to allow UTF-8 chars.
      Increased the --verb threshold for "PID_ERR replay" messages to 4 from 3.
      Fixed issue where redirect-gateway block-local code was not correctly calculating...
      CC_PRINT character class now allows any 8-bit character value >= 32.
      "status" management interface command (version >= 2) will now include the username for each connected user.
      Minor fix to CC_PRINT char class
      Fixed management interface bug where >FATAL notifications were not being output properly
      Raised D_PID_DEBUG_LOW from level 3 to 4 to reduce replay error verbosity at level 3.
      Added "memstats" option to maintain real-time operating stats in a memory-mapped file.
      Fixed client issues with DHCP Router option extraction/deletion when using layer 2 with DHCP proxy:
      Allow "tap-win32 dynamic <offset>" to be used in topology subnet mode.
      Added support for "on-link" routes on Linux client

Jan Just Keijser (1):
      Made some options connection-entry specific

Joe Patterson (1):
      common_name passing in auth_pam plugin

JuanJo Ciarlante (40):
      * rebased openvpn-2.1_rc1b.jjo.20061206.d.patch
      * created getaddr6(), use it from resolve_remote()
      * migrated all getaddrinfo() to getaddr6
      * socket.c: use USE_PF_INET6 in switch constructs to actually toss them out,
      * support --disable-ipv6 build properly:
      * important fix for tcp6 reconnection was incorrectly creating a PF_INET socket
      * added README.ipv6.txt
      * fixed win32 non-ipv6 build
      * ipv6 on win32 "milestone": 1st snapshot that passes all unittests
      * document ipv6 milestone status
      * doc update w/unittests results
      * make possible to x-compile openvpn/win32 in Linux
      * correctly setup hints.ai_socktype for getaddrinfo(), althought sorta hacky, see TODO.ipv6.
      * renamed README.ipv6{.txt,}
      * updated {README,TODO}.ipv6 from feedback at openvpn-devel mlist
      * init.c: document the ENABLE_MANAGEMENT place to work on
      * init.c: small in-doc tweaks
      * fix multi-tcp crash (corrected assertion)
      * TODO.ipv6 update
      * socket.c: better buf logic in print_sockaddr_ex
      * fixed segfault for undef address family in print_sockaddr_ex (thanks Marcel!)
      * doc updates
      * openbsd: no IFF_MULTICAST, #ifdef around it
      * no new funcionality, just small cleanups
      * (prototype) fix for supporting "redirect-gateway" for tunneled ipv4 over ipv6 endpoints
      * polished redirect-gateway (ipv4 on ipv6 endpoints) support
      * updated doc
      * fix --disable-ipv6 build
      * doc updates
      * rebased to v2.1.1 release
      * undo mroute.c changes related to ipv6 payload
      * fix --multihome for ipv4
      * fix --multihome for ipv6
      * ipv6-0.4.14: fix xinetd usage
      * ipv6-0.4.15: add --multihome support to xBSD
      * ipv6-0.4.15b: rebase over openvpn-testing-master
      * ipv6-0.4.16: fix mingw32 build
      * make ipv6_payload compile under windowze
      USE_PF_INET6 by default for v2.3
      fix ipv6 compilation under macosx >= 1070 - v3

Markus Koetter (1):
      Add extv3 X509 field support to --x509-username-field

Matthew L. Creech (1):
      Fix 2.2.0 build failure when management interface disabled

Matthias Andree (1):
      Skip rather than fail test in addressless FreeBSD jails.

Robert Fischer (8):
      Update man page with info about --capath
      Update man page with info about --connect-timeout
      Added info about --show-proxy-settings
      Documented --x509-username-field option
      Documented --errors-to-stderr option
      Documented --push-peer-info option
      Update man page with info about --remote-random-hostname
      Added man page entry for --management-client

Samuli Seppänen (19):
      Add man page entry for --redirect-private
      Change all CRLF linefeeds to LF linefeeds
      Fix a bug in devcon source code handling
      Removed Win2k from supported platforms list in INSTALL and win/openvpn.nsi
      Fixed copying of tapinstall.exe to dist/bin when using prebuilt TAP-drivers
      Fixed a bug with GUI icon deletion on upgrade from 2.2-RC or earlier
      Fix a build-ca issue on Windows
      Add new openssl.cnf to easy-rsa/Windows
      Updated "easy-rsa" for OpenSSL 1.0.0
      Made domake-win builds to use easy-rsa/2.0/openssl-1.0.0.cnf
      Fixes to easy-rsa/2.0
      Merged TODO.IPv6 with TODO.ipv6 and README.IPv6 with README.ipv6
      Fixed a number of fatal build errors on Visual Studio 2008
      Fix a Visual Studio 2008 build issue in socket.c
      Additional Visual Studio 2008 build fixes to tun.c
      Fixed a typo in win32.h that prevented building with Visual Studio
      Fixed a regression causing VS2008/Python build failure
      Fix a Visual Studio 2008 build error in tun.c
      Fix a Visual Studio 2008 build error in options.c

Simon Matter (1):
      Fix issues with some older GCC compilers

Stefan Hellermann (2):
      plugin.h: update prototype of plugin_call dummy in !ENABLE_PLUGIN case
      Fixed typo in plugin.h

chantra (1):
      Clarify --tmp-dir option

smos (1):
      Change the netsh.exe command from "add" to "set".

2011.12.25 -- Version 2.x-master
James Yonan (1):
      Added support for "on-link" routes on Linux client -- these are
      routes where the gateway is specified as an interface rather than
      an address.  This allows redirect-gateway to work on Linux clients
      whose connection to the internet is via a point-to-point link
      such as PPP.

      Note that at the moment, this capability is incompatible with
      the "redirect-gateway block-local" directive -- this is because
      the block-local directive blocks all traffic from the local LAN
      except for the local and gateway addresses.  Since a PPP link
      is essentially a subnet of two addresses, local and remote (i.e.
      gateway), the set of addresses that would be blocked by block-local
      is empty.  Therefore, the "redirect-gateway block-local" directive
      will be ignored on PPP links.

      To view the OpenVPN client's current determination of the default
      gateway, use this command:

        ./openvpn --show-gateway

2011.03.24 -- Version 2.2-RC2
Alon Bar-Lev (1):
      Windows cross-compile cleanup

David Sommerseth (2):
      Open log files as text files on Windows
      Clarify default value for the --inactive option.

Gert Doering (1):
      Implement IPv6 in TUN mode for Windows TAP driver.

Samuli Seppänen (6):
      Added support for prebuilt TAP-drivers. Automated embedding manifests.
      Fixes to win/openvpn.nsi
      Replaced config-win32.h with win/config.h.in
      Updated INSTALL-win32.txt
      Fixes to Makefile.am
      Clarified --client-config-dir section on the man-page.

Ville Skyttä (1):
      Fix line continuation in chkconfig init script description.

2011.02.28 -- Version 2.2-RC
David Sommerseth (3):
      Make the --x509-username-field feature an opt-in feature
      Fix compiler warning when compiling against OpenSSL 1.0.0
      Fix packaging of config-win32.h and service-win32/msvc.mak

James Yonan (1):
      Minor addition of logging info before and after execution of Windows net commands.

Matthias Andree (1):
      Change variadic macros to C99 style.

Samuli Seppänen (15):
      Added ENABLE_PASSWORD_SAVE to config-win32.h
      Added a nmake makefile for openvpnserv.exe building
      Moved TAP-driver version info to version.m4. Cleaned up win/settings.in.
      Added helper functionality to win/wb.py
      Added support for viewing config-win32.h paramters to win/show.py
      Added comments and made small modifications to win/msvc.mak.in
      Added command-line switch to win/build_all.py to skip TAP driver building
      Added configure.h and version.m4 variable parsing to win/config.py
      Added openvpnserv.exe building to win/build.py
      Added comments to win/build_ddk.py
      Several modifications to win/make_dist.py to allow building the NSI installer
      Copied install-win32/setpath.nsi to win/setpath.nsi
      Added first version of NSI installer script to win/openvpn.nsi
      Changes to buildsystem patchset
      Temporary snprintf-related fix to service-win32/openvpnserv.c

2010.11.25 -- Version 2.2-beta5

Samuli Seppänen (1):
      Fixed an issue causing a build failure with MS Visual Studio 2008.

2010.11.18 -- Version 2.2-beta4

David Sommerseth (10):
      Clarified --explicit-exit-notify man page entry
      Clean-up: Remove pthread and mutex locking code
      Clean-up: Remove more dead and inactive code paths
      Clean-up: Removing useless code - hash related functions
      Use stricter snprintf() formatting in socks_username_password_auth() (v3)
      Fix compiler warnings about not used dummy() functions
      Fixed potential misinterpretation of boolean logic
      Only add some functions when really needed
      Removed functions not being used anywhere
      Merged add_bypass_address() and add_host_route_if_nonlocal()

Gert Doering (3):
      Integrate support for TAP mode on Solaris, written by Kazuyoshi Aizawa <<EMAIL>>.
      Make "topology subnet" work on Solaris
      Improved man page entry for script_type

James Yonan (5):
      Fixed initialization bug in route_list_add_default_gateway (Gert Doering).
      Implement challenge/response authentication support in client mode
      Make base64.h have the same conditional compilation expression as base64.c.
      Fixed compiling issues when using --disable-crypto
      In verify_callback, the subject var should be freed by OPENSSL_free, not free

Jesse Young (1):
      Remove hardcoded path to resolvconf

Lars Hupel (1):
      Add HTTP/1.1 Host header

Pierre Bourdon (1):
      Adding support for SOCKS plain text authentication

Samuli Seppänen (2):
      Added check for variable CONFIGURE_DEFINES into options.c
      Added command-line option parser and an unsigned build option to build_all.py

2010.08.21 -- Version 2.2-beta3

* Attempt to fix issue where domake-win build system was not properly
  signing drivers and .exe files.

  Added win/tap_span.py for building multiple versions of the TAP driver
  and tapinstall binaries using different DDK versions to span from Win2K
  to Win7 and beyond.

* Community patches
  David Sommerseth (2):
      Test framework improvment - Do not FAIL if t_client.rc is missing
      More t_client.sh updates - exit with SKIP when we want to skip

  Gert Doering (4):
      Fix compile problems on NetBSD and OpenBSD
      Fix <net/if.h> compile time problems on OpenBSD for good
      full "VPN client connect" test framework for OpenVPN
      Build t_client.sh by configure at run-time.

  chantra (1):
      Fixes openssl-1.0.0 compilation warning

2010.08.16 -- Version 2.2-beta2

* Windows security issue:
  Fixed potential local privilege escalation vulnerability in
  Windows service. The Windows service did not properly quote the
  executable filename passed to CreateService.  A local attacker
  with write access to the root directory C:\ could create an
  executable that would be run with the same privilege level as
  the OpenVPN Windows service.  However, since non-Administrative
  users normally lack write permission on C:\, this vulnerability
  is generally not exploitable except on older versions of Windows
  (such as Win2K) where the default permissions on C:\ would allow
  any user to create files there.
  Credit:  Scott Laurie, MWR InfoSecurity

* Added Python-based based alternative build system for Windows using
  Visual Studio 2008 (in win directory).

* When aborting in a non-graceful way, try to execute do_close_tun in
  init.c prior to daemon exit to ensure that the tun/tap interface is
  closed and any added routes are deleted.

* Fixed an issue where AUTH_FAILED was not being properly delivered
  to the client when a bad password is given for mid-session reauth,
  causing the connection to fail without an error indication.

* Don't advance to the next connection profile on AUTH_FAILED errors.

* Fixed an issue in the Management Interface that could cause
  a process hang with 100% CPU utilization in --management-client
  mode if the management interface client disconnected at the
  point where credentials are queried.

* Fixed an issue where if reneg-sec was set to 0 on the client,
  so that the server-side value would take precedence,
  the auth_deferred_expire_window function would incorrectly
  return a window period of 0 seconds.  In this case, the
  correct window period should be the handshake window
  period.

* Modified ">PASSWORD:Verification Failed" management interface
  notification to include a client reason string:

    >PASSWORD:Verification Failed: 'AUTH_TYPE' ['REASON_STRING']

* Enable exponential backoff in reliability layer
  retransmits.

* Set socket buffers (SO_SNDBUF and SO_RCVBUF) immediately after
  socket is created rather than waiting until after connect/listen.

* Management interface performance optimizations:

  1. Added env-filter MI command to perform filtering on env vars
     passed through as a part of --management-client-auth

  2. man_write will now try to aggregate output into larger blocks
     (up to 1024 bytes) for more efficient i/o

* Fixed minor issue in Windows TAP driver DEBUG builds
  where non-null-terminated unicode strings were being
  printed incorrectly.

* Fixed issue on Windows with MSVC compiler, where TCP_NODELAY support
  was not being compiled in.

* Proxy improvements:

  Improved the ability of http-auth "auto" flag to dynamically detect
  the auth method required by the proxy.

  Added http-auth "auto-nct" flag to reject weak proxy auth methods.

  Added HTTP proxy digest authentication method.

  Removed extraneous openvpn_sleep calls from proxy.c.

* Implemented http-proxy-override and http-proxy-fallback directives to make it
  easier for OpenVPN client UIs to start a pre-existing client config file with
  proxy options, or to adaptively fall back to a proxy connection if a direct
  connection fails.

* Implemented a key/value auth channel from client to server.

* Fixed issue where bad creds provided by the management interface
  for HTTP Proxy Basic Authentication would go into an infinite
  retry-fail loop instead of requerying the management interface for
  new creds.

* Added support for MSVC debugging of openvpn.exe in settings.in:

  # Build debugging version of openvpn.exe
  !define PRODUCT_OPENVPN_DEBUG

* Implemented multi-address DNS expansion on the network field of route
  commands.

  When only a single IP address is desired from a multi-address DNS
  expansion, use the first address rather than a random selection.

* Added --register-dns option for Windows.

  Fixed some issues on Windows with --log, subprocess creation
  for command execution, and stdout/stderr redirection.

* Fixed an issue where application payload transmissions on the
  TLS control channel (such as AUTH_FAILED) that occur during
  or immediately after a TLS renegotiation might be dropped. 

* Added warning about tls-remote option in man page.

2009.12.11 -- Version 2.1.1

* Fixed some breakage in openvpn.spec (which is required to build an
  RPM distribution) where it was referencing a non-existent
  subdirectory in the tarball, causing it to fail (patch from
  David Sommerseth).

2009.12.11 -- Version 2.1.0

* Fixed a couple issues in sample plugins auth-pam.c and down-root.c.
  (1) Fail gracefully rather than segfault if calloc returns NULL.
  (2) The openvpn_plugin_abort_v1 function can potentially be called
  with handle == NULL.  Add code to detect this case, and if  so, avoid
  dereferencing pointers derived from handle  (Thanks to David
  Sommerseth for finding this bug).

* Documented "multihome" option in the man page.

2009.11.20 -- Version 2.1_rc22

* Fixed a client-side bug on Windows that occurred when the
  "dhcp-pre-release" or "dhcp-renew" options were combined with
  "route-gateway dhcp".  The release/renew would not occur
  because the Windows DHCP renew function is blocking and
  therefore must be called from another process or thread
  so as not to stall the tunnel.

* Added a hard failure when peer provides a certificate chain
  with depth > 16.  Previously, a warning was issued.

2009.11.12 -- Version 2.1_rc21

* Rebuilt OpenVPN Windows installer with OpenSSL 0.9.8l to address
  CVE-2009-3555.  Note that OpenVPN has never relied on the session
  renegotiation capabilities that are built into the SSL/TLS protocol,
  therefore the fix in OpenSSL 0.9.8l (disable SSL/TLS renegotiation
  completely) will not adversely affect OpenVPN mid-session SSL/TLS
  renegotation or any other OpenVPN capabilities.

* Added additional session renegotiation hardening.  OpenVPN has always
  required that mid-session renegotiations build up a new SSL/TLS
  session from scratch.  While the client certificate common name is
  already locked against changes in mid-session TLS renegotiations, we
  now extend this locking to the auth-user-pass username as well as all
  certificate content in the full client certificate chain.

2009.10.01 -- Version 2.1_rc20

* Fixed a bug introduced in 2.1_rc17 (svn r4436) where using the
  redirect-gateway option by itself, without any extra parameters,
  would cause the option to be ignored.

* Fixed build problem when ./configure --disable-server is used.

* Fixed ifconfig command for "topology subnet" on FreeBSD (Stefan Bethke).

* Added --remote-random-hostname option.

* Added "load-stats" management interface command to get global server
  load statistics.

* Added new ./configure flags:

  --disable-def-auth      Disable deferred authentication
  --disable-pf            Disable internal packet filter

* Added "setcon" directive for interoperability with SELinux (Sebastien
  Raveau).

* Optimized PUSH_REQUEST handshake sequence to shave several seconds
  off of a typical client connection initiation.

* The maximum number of "route" directives (specified in the config
  file or pulled from a server) can now be configured via the new
  "max-routes" directive.

* Eliminated the limitation on the number of options that can be pushed
  to clients, including routes.  Previously, all pushed options needed
  to fit within a 1024 byte options string.

* Added --server-poll-timeout option : when polling possible remote
  servers to connect to in a round-robin fashion, spend no more than
  n seconds waiting for a response before trying the next server.

* Added the ability for the server to provide a custom reason string
  when an AUTH_FAILED message is returned to the client.  This
  string can be set by the server-side managment interface and read
  by the client-side management interface.

* client-kill management interface command, when issued on server, will
  now send a RESTART message to client.
  This feature is intended to make UDP clients respond the same as TCP
  clients in the case where the server issues a RESTART message in
  order to force the client to reconnect and pull a new options/route
  list.

2009.07.16 -- Version 2.1_rc19

* In Windows TAP driver, refactor DHCP/ARP packet injection code to
  use a DPC (deferred procedure call) to defer packet injection until
  IRQL < DISPATCH_LEVEL, rather than calling NdisMEthIndicateReceive
  in the context of AdapterTransmit.  This is an attempt to reduce kernel
  stack usage, and prevent EXCEPTION_DOUBLE_FAULT BSODs that have been
  observed on Vista.  Updated TAP driver version number to 9.6.

* In configure.ac, use datadir instead of datarootdir for compatibility
  with <autoconf-2.60.

2009.06.07 -- Version 2.1_rc18

* Fixed compile error on ./configure --enable-small

* Fixed issue introduced in r4475 (2.1-rc17) where cryptoapi.c change
  does not build on Windows on non-MINGW32.

2009.05.30 -- Version 2.1_rc17

* Reduce the debug level (--verb) at which received management interface
  commands are echoed from 7 to 3.  Passwords will be filtered.

* Fixed race condition in management interface recv code on
  Windows, where sending a set of several commands to the
  management interface in quick succession might cause the
  latter commands in the set to be ignored.

* Increased management interface input command buffer size
  from 256 to 1024 bytes.

* Minor tweaks to Windows build system.

* Added "redirect-private" option which allows private subnets
  to be pushed to the client in such a way that they don't accidently
  obscure critical local addresses such as the DHCP server address and
  DNS server addresses.

* Added new 'autolocal' redirect-gateway flag.  When enabled, the OpenVPN
  client will examine the routing table and determine whether (a) the
  OpenVPN server is reachable via a locally connected interface, or (b)
  traffic to the server must be forwarded through the default router.
  Only add a special bypass route for the OpenVPN server if (b) is true.
  If (a) is true, behave as if the 'local' flag is specified, and do not
  add a bypass route.

  The new 'autolocal' flag depends on the non-portable test_local_addr()
  function in route.c, which is currently only implemented for Windows.
  The 'autolocal' flag will act as a no-op on platforms that have not
  yet defined a test_local_addr() function.

* Increased TLS_CHANNEL_BUF_SIZE to 2048 from 1024 (this will allow for
  more option content to be pushed from server to client).

* Raised D_MULTI_DROPPED debug level to 4 from 3 to filter out (at debug
  levels <=3) a common and usually innocuous warning.

* Fixed issue of symbol conflicts interfering with Windows CryptoAPI
  functionality (Alon Bar-Lev).

* Fixed bug where the remote_X environmental variables were not being
  set correctly when the 'local' option is specifed.

2009.05.17 -- Version 2.1_rc16

* Windows installer changes:

  1. ifdefed out the check Windows version code which is causing
  problems on Windows 7

  2. don't define SF_SELECTED if it is already defined

  3. Use LZMA instead of BZIP2 compression for better compression

  4. Upgraded OpenSSL to 0.9.8k

* Added the ability to read the configuration file
  from stdin, when "stdin" is given as the config
  file name.

* Allow "management-client" directive to be used
  with unix domain sockets.

* Added errors-to-stderr option.  When enabled, fatal errors
  that result in the termination of the daemon will be written
  to stderr.

* Added optional "nogw" (no gateway) flag to --server-bridge
  to inhibit the pushing of the route-gateway parameter to
  clients.

* Added new management interface command "pid" to show the
  process ID of the current OpenVPN process (Angelo Laub).

* Fixed issue where SIGUSR1 restarts would fail if private
  key was specified as an inline file.

* Added daemon_start_time and daemon_pid environmental variables.

* In management interface, added new ">CLIENT:ESTABLISHED" notification.

* Build fixes:

  1. Fixed some issues with C++ style comments that leaked into the code.

  2. Updated configure.ac to work on MinGW64.

  3. Updated common.h types for _WIN64.

  4. Fixed issue involving an #ifdef in a macro reference that breaks early gcc
     compilers.

  5. In cryptoapi.c, renamed CryptAcquireCertificatePrivateKey to
     OpenVPNCryptAcquireCertificatePrivateKey to work around
     a symbol conflict in MinGW-5.1.4.

2008.11.19 -- Version 2.1_rc15

* Fixed issue introduced in 2.1_rc14 that may cause a
  segfault when a --plugin module is used.

* Added server-side --opt-verify option: clients that connect
  with options that are incompatible with those of the server
  will be disconnected (without this option, incompatible
  clients would trigger a warning message in the server log
  but would not be disconnected).

* Added --tcp-nodelay option: Macro that sets TCP_NODELAY socket
  flag on the server as well as pushes it to connecting clients.

* Minor options check fix: --no-name-remapping is a
  server-only option and should therefore generate an
  error when used on the client.

* Added --prng option to control PRNG (pseudo-random
  number generator) parameters.  In previous OpenVPN
  versions, the PRNG was hardcoded to use the SHA1
  hash.  Now any OpenSSL hash may be used.  This is
  part of an effort to remove hardcoded references to
  a specific cipher or cryptographic hash algorithm.

* Cleaned up man page synopsis.

2008.11.16 -- Version 2.1_rc14

* Added AC_GNU_SOURCE to configure.ac to enable struct ucred,
  with the goal of fixing a build issue on Fedora 9 that was
  introduced in 2.1_rc13.

* Added additional method parameter to --script-security to preserve
  backward compatibility with system() call semantics used in OpenVPN
  2.1_rc8 and earlier.  To preserve backward compatibility use:

    script-security 3 system

* Added additional warning messages about --script-security 2
  or higher being required to execute user-defined scripts or
  executables.

* Windows build system changes:

  Modified Windows domake-win build system to write all openvpn.nsi
  input files to gen, so that gen can be disconnected from
  the rest of the source tree and makensis openvpn.nsi will
  still function correctly.

  Added additional SAMPCONF_(CA|CRT|KEY) macros to settings.in
  (commented out by default).

  Added optional files SAMPCONF_CONF2 (second sample configuration
  file) and SAMPCONF_DH (Diffie-Helman parameters) to Windows
  build system, and may be defined in settings.in.

* Extended Management Interface "bytecount" command
  to work when OpenVPN is running as a server.
  Documented Management Interface "bytecount" command in
  management/management-notes.txt.

* Fixed informational message in ssl.c to properly indicate
  deferred authentication.

* Added server-side --auth-user-pass-optional directive, to allow
  connections by clients that do not specify a username/password, when a
  user-defined authentication script/module is in place (via
  --auth-user-pass-verify, --management-client-auth, or a plugin module).

* Changes to easy-rsa/2.0/pkitool and related openssl.cnf:

  Calling scripts can set the KEY_NAME environmental variable to set
  the "name" X509 subject field in generated certificates.

  Modified pkitool to allow flexibility in separating the Common Name
  convention from the cert/key filename convention.

  For example:

  KEY_CN="James's Laptop" KEY_NAME="james" ./pkitool james

  will create a client certificate/key pair of james.crt/james.key
  having a Common Name of "James's Laptop" and a Name of "james".

* Added --no-name-remapping option to allow Common Name, X509 Subject,
  and username strings to include any printable character including
  space, but excluding control characters such as tab, newline, and
  carriage-return (this is important for compatibility with external
  authentication systems).

  As a related change, added --status-version 3 format (and "status 3"
  in the management interface) which uses the version 2 format except
  that tabs are used as delimiters instead of commas so that there
  is no ambiguity when parsing a Common Name that contains a comma.

  Also, save X509 Subject fields to environment, using the naming
  convention:

  X509_{cert_depth}_{name}={value}

  This is to avoid ambiguities when parsing out the X509 subject string
  since "/" characters could potentially be used in the common name.

* Fixed some ifconfig-pool issues that precluded it from being combined
  with --server directive.

  Now, for example, we can configure thusly:

    server 10.8.0.0 ************* nopool
    ifconfig-pool 10.8.0.2 10.8.0.99 *************

  to have ifconfig-pool manage only a subset
  of the VPN subnet.

* Added config file option "setenv FORWARD_COMPATIBLE 1" to relax
  config file syntax checking to allow directives for future OpenVPN
  versions to be ignored.

2008.10.07 -- Version 2.1_rc13

* Bundled OpenSSL 0.9.8i with Windows installer.

* Management interface can now listen on a unix
  domain socket, for example:

    management /tmp/openvpn unix

  Also added management-client-user and management-client-group
  directives to control which processes are allowed to connect
  to the socket.

* Copyright change to OpenVPN Technologies, Inc.

2008.09.23 -- Version 2.1_rc12

* Patched Makefile.am so that the new t_cltsrv-down.sh script becomes
  part of the tarball (Matthias Andree).

* Fixed --lladdr bug introduced in 2.1-rc9 where input validation code
  was incorrectly expecting the lladdr parameter to be an IP address
  when it is actually a MAC address (HoverHell).

2008.09.14 -- Version 2.1_rc11

* Fixed a bug that can cause SSL/TLS negotiations in UDP mode
  to fail if UDP packets are dropped.

2008.09.10 -- Version 2.1_rc10

* Added "--server-bridge" (without parameters) to enable
  DHCP proxy mode:  Configure server mode for ethernet
  bridging using a DHCP-proxy, where clients talk to the
  OpenVPN server-side DHCP server to receive their IP address
  allocation and DNS server addresses.

* Added "--route-gateway dhcp", to enable the extraction
  of the gateway address from a DHCP negotiation with the
  OpenVPN server-side LAN.

* Fixed minor issue with --redirect-gateway bypass-dhcp or bypass-dns
  on Windows.  If the bypass IP address is 0.0.0.0 or ***************,
  ignore it.

* Warn when ethernet bridging that the IP address of the bridge adapter
  is probably not the same address that the LAN adapter was set to
  previously.

* When running as a server, warn if the LAN network address is
  the all-popular 192.168.[0|1].x, since this condition commonly
  leads to subnet conflicts down the road.

* Primarily on the client, check for subnet conflicts between
  the local LAN and the VPN subnet.

* Added a 'netmask' parameter to get_default_gateway, to return
  the netmask of the adapter containing the default gateway.
  Only implemented on Windows so far.  Other platforms will
  return *************.  Currently the netmask information is
  only used to warn about subnet conflicts.

* Minor fix to cryptoapi.c to not compile itself unless USE_CRYPTO
  and USE_SSL flags are enabled (Alon Bar-Lev).

* Updated openvpn/t_cltsrv.sh (used by "make check") to conform to new
  --script-security rules.  Also adds retrying if the addresses are in
  use (Matthias Andree).

* Fixed build issue with ./configure --disable-socks --disable-http.

* Fixed separate compile errors in options.c and ntlm.c that occur
  on strict C compilers (such as old versions of gcc) that require
  that C variable declarations occur at the start of a {} block,
  not in the middle.

* Workaround bug in OpenSSL 0.9.6b ASN1_STRING_to_UTF8, which
  the new implementation of extract_x509_field_ssl depends on.

* LZO compression buffer overflow errors will now invalidate
  the packet rather than trigger a fatal assertion.

* Fixed minor compile issue in ntlm.c (mid-block declaration).

* Added --allow-pull-fqdn option which allows client to pull DNS names
  from server (rather than only IP address) for --ifconfig, --route, and
  --route-gateway.  OpenVPN versions 2.1_rc7 and earlier allowed DNS names
  for these options to be pulled and translated to IP addresses by default.
  Now --allow-pull-fqdn will be explicitly required on the client to enable
  DNS-name-to-IP-address translation of pulled options.

* 2.1_rc8 and earlier did implicit shell expansion on script
  arguments since all scripts were called by system().
  The security hardening changes made to 2.1_rc9 no longer
  use system(), but rather use the safer execve or CreateProcess
  system calls.  The security hardening also introduced a
  backward incompatibility with 2.1_rc8 and earlier in that
  script parameters were no longer shell-expanded, so
  for example:

    client-connect "docc CLIENT-CONNECT"

  would fail to work because execve would try to execute
  a script called "docc CLIENT-CONNECT" instead of "docc"
  with "CLIENT-CONNECT" as the first argument.

  This patch fixes the issue, bringing the script argument
  semantics back to pre 2.1_rc9 behavior in order to preserve
  backward compatibility while still using execve or CreateProcess
  to execute the script/executable.

* Modified ip_or_dns_addr_safe, which validates pulled DNS names,
  to more closely conform to RFC 3696:

  (1) DNS name length must not exceed 255 characters

  (2) DNS name characters must be limited to alphanumeric,
      dash ('-'), and dot ('.')

* Fixed bug in intra-session TLS key rollover that was introduced with
  deferred authentication features in 2.1_rc8.

2008.07.31 -- Version 2.1_rc9

* Security Fix -- affects non-Windows OpenVPN clients running
  OpenVPN 2.1-beta14 through 2.1-rc8 (OpenVPN 2.0.x clients are NOT
  vulnerable nor are any versions of the OpenVPN server vulnerable).
  An OpenVPN client connecting to a malicious or compromised
  server could potentially receive an "lladdr" or "iproute" configuration
  directive from the server which could cause arbitrary code execution on
  the client. A successful attack requires that (a) the client has agreed
  to allow the server to push configuration directives to it by including
  "pull" or the macro "client" in its configuration file, (b) the client
  successfully authenticates the server, (c) the server is malicious or has
  been compromised and is under the control of the attacker, and (d) the
  client is running a non-Windows OS.  Credit: David Wagner.
  CVE-2008-3459

* Miscellaneous defensive programming changes to multiple
  areas of the code.  In particular, use of the system() call
  for calling executables such as ifconfig, route, and
  user-defined scripts has been completely revamped in favor
  of execve() on unix and CreateProcess() on Windows.

* In Windows build, package a statically linked openssl.exe to work around
  observed instabilities in the dynamic build since the migration to
  OpenSSL 0.9.8h.

2008.06.11 -- Version 2.1_rc8

* Added client authentication and packet filtering capability
  to management interface.  In addition, allow OpenVPN plugins
  to take advantage of deferred authentication and packet
  filtering capability.

* Added support for client-side connection profiles.

* Fixed unbounded memory growth bug in environmental variable
  code that could have caused long-running OpenVPN sessions
  with many TLS renegotiations to incrementally
  increase memory usage over time.

* Windows release now packages openssl-0.9.8h.

* Build system changes -- allow building on Windows using
  autoconf/automake scripts (Alon Bar-Lev).

* Changes to Windows build system to make it easier to do
  partial builds, with a reduced set of prerequisites,
  where only a subset of OpenVPN installer
  components are built.  See ./domake-win comments.

* Cleanup IP address for persistence interfaces for tap and also
  using ifconfig, gentoo#209055 (Alon Bar-Lev).

* Fall back to old version of extract_x509_field for OpenSSL 0.9.6.

* Clarified tcp-queue-limit man page entry (Matti Linnanvuori).

* Added new OpenVPN icon and installer graphic.

* Minor pkitool changes.

* Added --pkcs11-id-management option, which will cause OpenVPN to
  query the management interface via the new NEED-STR asynchronous
  notification query to get additional PKCS#11 options (Alon Bar-Lev).

* Added NEED-STR management interface asynchronous query and
  "needstr" management interface command to respond to the query
  (Alon Bar-Lev).

* Added Dragonfly BSD support (Francis-Gudin).

* Quote device names before passing to up/down script (Josh Cepek).

* Bracketed struct openvpn_pktinfo with #pragma pack(1) to
  prevent structure padding from causing an incorrect length
  to be returned by sizeof (struct openvpn_pktinfo) on 64-bit
  platforms.

* On systems that support res_init, always call it
  before calling gethostbyname to ensure that
  resolver configuration state is current.

* Added NTLMv2 proxy support (Miroslav Zajic).

* Fixed an issue in extract_x509_field_ssl where the extraction
  would fail on the first field of the subject name, such as
  the common name in:  /CN=foo/emailAddress=<EMAIL>

* Made "Linux ip addr del failed" error nonfatal.

* Amplified --client-cert-not-required warning.

* Added #pragma pack to proto.h.

2008.01.29 -- Version 2.1_rc7

* Added a few extra files that exist in the svn repo but were
  not being copied into the tarball by make dist.

* Fixup null interface on close, don't use ip addr flush (Alon Bar-Lev).

2008.01.24 -- Version 2.1_rc6

* Fixed options checking bug introduced in rc5 where legitimate configuration
  files might elicit the error: "Options error: Parameter pkcs11_private_mode
  can only be specified in TLS-mode, i.e. where --tls-server or --tls-client
  is also specified."
	
2008.01.23 -- Version 2.1_rc5

* Fixed Win2K TAP driver bug that was introduced by Vista fixes,
  incremented driver version to 9.4.

* Windows build system changes:

  Incremented included OpenSSL version to openssl-0.9.7m.

  Updated openssl.patch for openssl-0.9.7m and added some
  brief usage comments to the head of the patch.

  Added build-pkcs11-helper.sh for building the pkcs11-helper
  library.

  Integrated inclusion of pkcs11-helper into Windows build
  system.

  Upgraded TAP build scripts to use WDK 6001.17121
  (Windows 2008 Server pre-RTM).

* Windows installer changes:

  Clean up the start menu folder.

  Allow for a site-specific sample configuration file and keys
  to be included in a custom installer (see SAMPCONF macros
  in settings.in). 

  New icon (temporary).

* Added "forget-passwords" command to the management interface
  (Alon Bar-Lev).

* Added --management-signal option to signal SIGUSR1 when the
  management interface disconnects (Alon Bar-Lev).

* Modified command line and config file parser to allow
  quoted strings using single quotes ('') (Alon Bar-Lev).

* Use pkcs11-helper as external library, can be downloaded from
  https://www.opensc-project.org/pkcs11-helper (Alon Bar-Lev).

* Fixed interim memory growth issue in TCP connect loop where
  "TCP: connect to %s failed, will try again in %d seconds: %s"
  is output.

* Fixed bug in epoll driver in event.c, where the lack of a
  handler for EPOLLHUP could cause 99% CPU usage.

* Defined ALLOW_NON_CBC_CIPHERS for people who don't
  want to use a CBC cipher for OpenVPN's data channel.

* Added PLUGIN_LIBDIR preprocessor string to prepend a default
  plugin directory to the dlopen search list when the user
  specifies the basename of the plugin only (Marius Tomaschewski).

* Rewrote extract_x509_field and modified COMMON_NAME_CHAR_CLASS
  to allow forward slash characters ("/") in the X509 common name
  (Pavel Shramov).

* Allow OpenVPN to run completely unprivileged under Linux
  by allowing openvpn --mktun to be used with --user and --group
  to set the UID/GID of the tun device node.  Also added --iproute
  option to allow an alternative command to be executed in place
  of the default iproute2 command (Alon Bar-Lev).

* Fixed --disable-iproute2 in ./configure to actually disable
  iproute2 usage (Alon Bar-Lev).

* Added --management-forget-disconnect option -- forget
  passwords when management session disconnects (Alon Bar-Lev).
	
2007.04.25 -- Version 2.1_rc4

* Worked out remaining issues with TAP driver signing
  on Vista x64.  OpenVPN will now run on Vista x64
  with driver signing enforcement enabled.

* Fixed 64-bit portability bug in time_string function
  (Thomas Habets).

2007.04.22 -- Version 2.1_rc3

* Additional fixes to TAP driver for Windows x64.  Driver
  now runs successfully on Vista x64 if driver signing
  enforcement is disabled.
	
* The Windows Installer and TAP driver are now signed by
  OpenVPN Solutions LLC (in addition to the usual GnuPG
  signatures).

* Added OpenVPN GUI (Mathias Sundman version) as install
  option in Windows installer.

* Clean up configure on FreeBSD for recent autotool versions
  that require that all .h files have to be compiled.
  Also, FreeBSD install does not support GNU long options
  which the Makefile in easy-rsa/2.0 uses (not checked the
  others as we don't install those on Gentoo) (Roy Marples).

* Added additional scripts to easy-rsa/Windows for working
  with password-protected keys; also add -extensions server
  option when generating server cert via
  build-key-server-pass.bat (Daniel Zauft).
	
2007.02.27 -- Version 2.1_rc2

* auth-pam change:  link with -lpam rather
  than dlopen (Roy Marples).

* Prevent SIGUSR1 or SIGHUP from causing program
  exit from initial management hold.

* SO_REUSEADDR should not be set on Windows TCP sockets
  because it will cause bind to succeed on port conflicts.

* Added time_ascii, time_duration, and time_unix
  environmental variables for plugins and callback
  scripts.

* Fixed issue where OpenVPN does not apply the --txqueuelen option
  to persistent interfaces made with --mktun (Roy Marples).

* Attempt at rational signal handling when in the
  management hold state.  During management hold, ignore
  SIGUSR1/SIGHUP signals thrown with the "signal" command.
  Also, "signal" command will now apply remapping as
  specified with the --remap-usr1 option.
  When a signal entered using the "signal" command from a management
  hold is ignored, output: >HOLD:Waiting for hold release

* Fixed issue where struct env_set methods that
  change the value of an existing name=value pair
  would delay the freeing of the memory held by
  the previous name=value pair until the underlying
  client instance object is closed.
  This could cause a server that handles long-term
  client connections, resulting in many periodic calls
  to verify_callback, to needlessly grow the env_set
  memory allocation until the underlying client instance
  object is closed.

* Renamed TAP-Win32 driver from tap0801.sys to tap0901.sys
  to reflect the fact that Vista has blacklisted the tap0801.sys
  file name due to previous compatibility issues which have now
  been resolved.  TAP-Win32 major/minor version number is now 9/1.

* Windows installer will delete a previously installed
  tap0801.sys TAP driver before installing tap0901.sys.

* Added code to Windows installer to fail gracefully on 64 bit
  installs until 64-bit TAP driver issues can be resolved.

* Added code to Windows installer to fail gracefully on
  versions of Windows which are not explicitly supported.

* The Windows version will now use a default route-delay
  of 5 seconds to deal with an apparent routing table race
  condition on Vista.

* Worked around an incompatibility in the Windows Vista
  version of CreateIpForwardEntry as described in
  http://www.nynaeve.net/?p=59
  This issue would cause route additions using the
  IP Helper API to fail on Vista.

* On Windows, revert to "ip-win32 dynamic" as the default.

2006.10.31 -- Version 2.1_rc1

* Support recovery (return to hold) from signal at
  management password prompt.

* Added workaround for OpenSC PKCS#11 bug#108
  (Alon Bar-Lev).

2006.10.01 -- Version 2.1-beta16

* Windows installer updated with OpenSSL 0.9.7l DLLs to fix
  published vulnerabilities.

* Fixed TAP-Win32 bug that caused BSOD on Windows Vista
  (Henry Nestler).
	
* Autodetect 32/64 bit Windows in installer and install
  appropriate TAP driver (Mathias Sundman, Hypherion).
	
* Fixed bug in loopback self-test introduced
  in 2.1-beta15 where self test as invoked by
  "make check" would not properly exit after
  2 minutes (Paul Howarth).

2006.09.12 -- Version 2.1-beta15

* Windows installer updated with OpenSSL 0.9.7k DLLs to fix
  RSA Signature Forgery (CVE-2006-4339).

* Fixed bug introduced with the --port-share directive
  (back in 2.1-beta9 which causes TLS soft resets
  (1 per hour by default) in TCP server mode to force
  a blockage of tunnel packets and later time-out and
  restart the connection.

* easy-rsa update (Alon Bar-Lev)
  Makefile (install) is now available so that
  distribs will be able to install it safely.

* PKCS#11 changes: (Alon Bar-Lev) 
  - Modified ssl.c to not FATAL and return to init.c
    so auth-retry will work.
  - Modifed pkcs11-helper.c to fix some problem with
    multiple providers.
  - Added retry counter to PKCS#11 PIN hook.
  - Modified PKCS#11 PIN retry loop to return correct error
    code when PIN is incorrect.
  - Fix handling (ignoring) zero sized attributes.
  - Fix gcc-2 issues.
  - Fix openssl 0.9.6 (first version) issues.

* Minor fixes of lladdr (Alon Bar-Lev)
  Updated makefile.w32-vc to include lladdr.*, updated
  linkage libraries.
  Modified lladdr.c to be compiled under visual C.

* Added two new management states:
   OPENVPN_STATE_RESOLVE      -- DNS lookup
   OPENVPN_STATE_TCP_CONNECT  -- Connecting to TCP server

* Echo management state change to log.

* Minor syshead.h change for NetBSD to allow
  TCP_NODELAY flag to work.

* Modified --port-share code to remove the assumption that
  CMSG_SPACE always evaluates to a constant, to enable
  compilation on NetBSD and possibly other BSDs as well.

* Eliminated gcc 3.3.3 warnings on NetBSD
  when ./configure --enable-strict is used.

* Added optional minimum-number-of-bytes parameter
  to --inactive directive.

2006.04.13 -- Version 2.1-beta14

* Fixed Windows server bug in time backtrack handling code which
  could cause TLS negotiation failures on legitimate clients.
	
* Rewrote gettimeofday function for Windows to be
  simpler and more efficient.
	
* Merged PKCS#11 extensions to easy-rsa/2.0  (Alon Bar-Lev).

* Added --route-metric option to set a default route metric
  for --route (Roy Marples).

* Added --lladdr option to specify the link layer (MAC) address
  for the tap interface on non-Windows platforms (Roy Marples).

2006.04.12 -- Version 2.1-beta13

* Code added in 2.1-beta7 and 2.0.6-rc1 to extend byte counters
  to 64 bits caused a bug in the Windows version which has now
  been fixed.  The bug could cause intermittent crashes.
	
2006.04.05 -- Version 2.1-beta12

* Security Vulnerability -- An OpenVPN client connecting to a
  malicious or compromised server could potentially receive
  "setenv" configuration directives from the server which could
  cause arbitrary code execution on the client via a LD_PRELOAD
  attack.  A successful attack appears to require that (a) the
  client has agreed to allow the server to push configuration
  directives to it by including "pull" or the macro "client" in
  its configuration file, (b) the client configuration file uses
  a scripting directive such as "up" or "down", (c) the client
  succesfully authenticates the server, (d) the server is
  malicious or has been compromised and is under the control of
  the attacker, and (e) the attacker has at least some level of
  pre-existing control over files on the client (this might be
  accomplished by having the server respond to a client web request
  with a specially crafted file).  Credit: Hendrik Weimer.
  CVE-2006-1629.

  The fix is to disallow "setenv" to be pushed to clients from
  the server, and to add a new directive "setenv-safe" which is
  pushable from the server, but which appends "OPENVPN_" to the
  name of each remotely set environmental variable.

* "topology subnet" fix for FreeBSD (Benoit Bourdin).

* PKCS11 fixes (Alon Bar-Lev).  For full description:
  svn log -r990 http://svn.openvpn.net/projects/openvpn/branches/BETA21
	
* When deleting routes under Linux, use the route metric
  as a differentiator to ensure that the route teardown
  process only deletes the identical route which was originally
  added via the "route" directive (Roy Marples).

* Fix the t_cltsrv.sh file in FreeBSD 4 jails
  (Matthias Andree, Dirk Meyer, Vasil Dimov).

* Extended tun device configure code to support ethernet
  bridging on NetBSD (Emmanuel Kasper).

2006.02.19 -- Version 2.1-beta11

* Fixed --port-share bug that caused premature closing
  of proxied sessions.

2006.02.17 -- Version 2.1-beta10

* Fixed --port-share breakage introduced in 2.1-beta9.

2006.02.16 -- Version 2.1-beta9

* Added --port-share option for allowing OpenVPN and HTTPS
  server to share the same port number.
* Added --management-client option to connect as a client
  to management GUI app rather than be connected to as a
  server.
* Added "bytecount" command to management interface.
* --remote-cert-tls fixes (Alon Bar-Lev).

2006.01.03 -- Version 2.1-beta8

* --remap-usr1 will now also remap signals thrown during
  initialization.
* Added --connect-timeout option to control the timeout
  on TCP client connection attempts (doesn't work on all
  OSes).  This patch also makes OpenVPN signalable during
  TCP connection attempts.
* Fixed bug in acinclude.m4 where capability of compiler
  to handle zero-length arrays in structs is tested
  (David Stipp).
* Fixed typo in manage.c where inline function declaration
  was declared without the "static" keyword (David Stipp).
* Patch to support --topology subnet on Mac OS X (Mathias Sundman).
* Added --auto-proxy directive to auto-detect HTTP or SOCKS
  proxy settings (currently Windows only).
* Removed redundant base64 code.
* Better sanity checking of --server and --server-bridge
  IP pool ranges, so as not to hit the assertion at
  pool.c:119 (2.0.5).
* Fixed bug where --daemon and --management-query-passwords
  used together would cause OpenVPN to block prior to
  daemonization.
* Fixed client/server race condition which could occur
  when --auth-retry interact is set and the initially
  provided auth-user-pass credentials are incorrect,
  forcing a username/password re-query.
* Fixed bug where if --daemon and --management-hold are
  used together, --user or --group options would be ignored.
* --ip-win32 adaptive is now the default.
* --ip-win32 netsh (or --ip-win32 adaptive when in netsh
  mode) can now set DNS/WINS addresses on the TAP-Win32
  adapter.
* Added new option --route-method adaptive (Win32)
  which tries IP helper API first, then falls back to
  route.exe.
* Made --route-method adaptive the default.
	
2005.11.12 -- Version 2.1-beta7

* Allow blank passwords to be passed via the management
  interface.
* Fixed bug where "make check" inside a FreeBSD "jail"
  would never complete (Matthias Andree).
* Fixed bug where --server directive in --dev tap mode
  claimed that it would support subnets of /30 or less
  but actually would only accept /29 or less.
* Extend byte counters to 64 bits (M. van Cuijk).
* Fixed bug in Linux get_default_gateway function
  introduced in 2.0.4, which would cause redirect-gateway
  on Linux clients to fail.
* Moved easy-rsa 2.0 scripts to easy-rsa/2.0 to
  be compatible with 2.0.x distribution.
* Documented --route-nopull.
* Documented --ip-win32 adaptive.
* Windows build now linked with LZO2.
* Allow ca, cert, key, and dh files to be specified
  inline via XML-like syntax without needing to
  reference an explicit file.
  For example:
  <ca>
	data here...
  </ca>
* Allow plugin and push directives to have multi-line
  parameter lists such as:
  <plugin>
    my-plugin.so
    parm1
    parm2
  </plugin>
* Added connect-retry-max option (Alon Bar-Lev).
* Fixed problems where signals thrown during initialization
  were not returning to a management-hold state.
* Added a backtrack-hardened system time algorithm.
* Added --remote-cert-ku, --remote-cert-eku, and
  --remote-cert-tls options for verifying certificate
  attributes (Alon Bar-Lev).
* For Windows, reverted --ip-win32 default back to "dynamic".
  To use new adaptive mode, set explicitly.
	
2005.11.01 -- Version 2.1-beta6

* Security fix (merged from 2.0.4) -- Affects non-Windows
  OpenVPN clients of version 2.0 or higher which connect to
  a malicious or compromised server.  A format string
  vulnerability in the foreign_option function in options.c
  could potentially allow a malicious or compromised server
  to execute arbitrary code on the client.  Only
  non-Windows clients are affected.  The vulnerability
  only exists if (a) the client's TLS negotiation with
  the server succeeds, (b) the server is malicious or
  has been compromised such that it is configured to
  push a maliciously crafted options string to the client,
  and (c) the client indicates its willingness to accept
  pushed options from the server by having "pull" or
  "client" in its configuration file (Credit: Vade79).
  CVE-2005-3393
* Security fix -- (merged from 2.0.4) Potential DoS
  vulnerability on the server in TCP mode.  If the TCP
  server accept() call returns an error status, the resulting
  exception handler may attempt to indirect through a NULL
  pointer, causing a segfault.  Affects all OpenVPN 2.0 versions.
  CVE-2005-3409
* Fix attempt of assertion at multi.c:1586 (note that
  this precise line number will vary across different
  versions of OpenVPN).
* Windows reliability changes:
  (a) Added code to make sure that the local PATH environmental
      variable points to the Windows system32 directory.
  (b) Added new --ip-win32 adaptive mode which tries 'dynamic'
      and then fails over to 'netsh' if the DHCP negotiation fails.
  (c) Made --ip-win32 adaptive the default.
* More PKCS#11 additions/changes (Alon Bar-Lev).
* Added ".PHONY: plugin" to Makefile.am to work around
  "make dist" issue.
* Fixed double fork issue that occurs when --management-hold
  is used.
* Moved TUN/TAP read/write log messages from --verb 8 to 6.
* Warn when multiple clients having the same common name or
  username usurp each other when --duplicate-cn is not used.
* Modified Windows and Linux versions of get_default_gateway
  to return the route with the smallest metric
  if multiple 0.0.0.0/0.0.0.0 entries are present.
* Added ">NEED-OK" alert and "needok" command to management
  interface to provide a general interface for sending
  alerts to the end-user.  Used by the PKCS#11 code
  to send Token Insertion Requests to the user.
* Added actual remote address used to the ">STATE" alert
  in the management interface (Rolf Fokkens).

2005.10.17 -- Version 2.1-beta4

* Fixed bug introduced in 2.1-beta3 where management
  socket bind would fail.
* --capath fix in ssl.c (Zhuang Yuyao).
* Added ".PHONY: plugin" to Makefile.am, reverted
  location of "plugin" directory (thanks to
  Matthias Andree for figuring this out).
	
2005.10.16 -- Version 2.1-beta3

* Added PKCS#11 support (Alon Bar-Lev).
* Enable the use of --ca together with --pkcs12.  If --ca is
  used at the same time as --pkcs12, the CA certificate is loaded
  from the file specified by --ca regardless if the pkcs12 file
  contains a CA cert or not (Mathias Sundman).
* Merged --capath patch (Thomas Noel).
* Merged --multihome patch.
* Added --bind option for TCP client connections (Ewan Bhamrah
  Harley).
* Moved "plugin" directory to "plugins" to deal with strange
  automake problem that ended up being also fixable with
  ".PHONY: plugin" in Makefile.am.

2005.10.13 -- Version 2.1-beta2

* Made --sndbuf and --rcvbuf pushable.

2005.10.01 -- Version 2.1-beta1

* Made LZO setting pushable.
* Renamed sample-keys/tmp-ca.crt to ca.crt.
* Fixed bug where remove_iroutes_from_push_route_list
  was missing routes if those routes had
  an implied netmask (by omission) of ***************. 
* Merged with 2.0.3-rc1
* easy-rsa/2.0 moved to easy-rsa
* old easy-rsa moved to easy-rsa/1.0

2005.09.23 -- Version 2.0.2-TO4

* Added feature to TAP-Win32 adapter to allow it to be
  opened from non-administrator mode.  This feature
  is enabled by default, and can be enabled/disabled
  in the adapter advanced properties dialog.
* Added --allow-nonadmin standalone option for Windows to
  set TAP adapter to allow non-admin access.  This
  is a user-mode version of the code, and duplicates
  the same feature as the above entry.
* Added fix that attempts to solve corner case of tunnel not
  forwarding packets when system clock is reset to an earlier time.
* Added --redirect-gateway bypass-dns option.  (Developers:
  To add bypass-dhcp or bypass-dns support to other OSes,
  add a get_bypass_addresses function to route.c for
  your OS.)
* Added OPENVPN_PLUGIN_CLIENT_CONNECT_V2 plugin callback, which
  allows a client-connect plugin to return configuration text
  in memory, rather than via a file.
* Fixed a bug where --mode server --proto tcp-server --cipher none
  operation could cause tunnel packet truncation.
* openvpn --version will show [LZO1] or [LZO2], depending on
  version that was linked.

2005.09.07 -- Version 2.0.2-TO1

* Added --topology directive.  See man page.
* Added --redirect-gateway bypass-dhcp option to add a route
  allowing DHCP packets to bypass the tunnel, when the
  DHCP server is non-local.  Currently only implemented
  on Windows clients.
* Modified OpenVPN Service on Windows to declare the DHCP
  client service as a dependency.
* Extended the plugin interface to allow plugins to declare
  per-client constructor and destructor functions, to make
  it simpler for plugins to maintain per-client state.

2005.09.25 -- Version 2.0.3-rc1	

* openvpn_plugin_abort_v1 function wasn't being properly
  registered on Windows.
* Fixed a bug where --mode server --proto tcp-server --cipher none
  operation could cause tunnel packet truncation.

2005.08.25 -- Version 2.0.2

* No change from 2.0.2-rc1.

2005.08.24 -- Version 2.0.2-rc1

* Fixed regression bug in Win32 installer, introduced in 2.0.1,
  which incorrectly set OpenVPN service to autostart.
* Don't package source code zip file in Windows installer
  in order to reduce the size of the installer.  The source
  zip file can always be downloaded separately if needed.
* Fixed bug in route.c in FreeBSD, Darwin, OpenBSD and NetBSD
  version of get_default_gateway.  Allocated socket for route
  manipulation is never freed so number of mbufs continuously
  grow and exhaust system resources after a while (Jaroslav Klaus).
* Fixed bug where "--proto tcp-server --mode p2p --management
  host port" would cause the management port to not respond until
  the OpenVPN peer connects.
* Modified pkitool script to be /bin/sh compatible (Johnny Lam).

2005.08.16 -- Version 2.0.1
	
* Security Fix -- DoS attack against server when run with "verb 0" and
  without "tls-auth".  If a client connection to the server fails
  certificate verification, the OpenSSL error queue is not properly
  flushed, which can result in another unrelated client instance on the
  server seeing the error and responding to it, resulting in disconnection
  of the unrelated client (CAN-2005-2531).
* Security Fix -- DoS attack against server by authenticated client.
  This bug presents a potential DoS attack vector against the server
  which can only be initiated by a connected and authenticated client.
  If the client sends a packet which fails to decrypt on the server,
  the OpenSSL error queue is not properly flushed, which can result in
  another unrelated client instance on the server seeing the error and
  responding to it, resulting in disconnection of the unrelated client
  (CAN-2005-2532).  Credit: Mike Ireton.
* Security Fix -- DoS attack against server by authenticated client.
  A malicious client in "dev tap" ethernet bridging mode could
  theoretically flood the server with packets appearing to come from
  hundreds of thousands of different MAC addresses, causing the OpenVPN
  process to deplete system virtual memory as it expands its internal
  routing table.  A --max-routes-per-client directive has been added
  (default=256) to limit the maximum number of routes in OpenVPN's
  internal routing table which can be associated with a given client
  (CAN-2005-2533).
* Security Fix -- DoS attack against server by authenticated client.
  If two or more client machines try to connect to the server at the
  same time via TCP, using the same client certificate, and when
  --duplicate-cn is not enabled on the server, a race condition can
  crash the server with "Assertion failed at mtcp.c:411"
  (CAN-2005-2534).
* Fixed server bug where under certain circumstances, the client instance
  object deletion function would try to delete iroutes which had never been
  added in the first place, triggering "Assertion failed at mroute.c:349".
* Added --auth-retry option to prevent auth errors from being fatal
  on the client side, and to permit username/password requeries in case
  of error.  Also controllable via new "auth-retry" management interface
  command.  See man page for more info.
* Added easy-rsa 2.0 scripts to the tarball in easy-rsa/2.0
* Fixed bug in openvpn.spec where rpmbuild --define 'without_pam 1'
  would fail to build.
* Implement "make check" to perform loopback tests (Matthias Andree).

2005.07.21 -- Version 2.0.1-rc7

* Support LZO 2.01 which renamed its library to lzo2 (Matthias Andree).
* Include linux/types.h before checking for linux/errqueue.h (Matthias
  Andree).

2005.07.15 -- Version 2.0.1-rc6

* Commented out "user nobody" and "group nobody" in sample
  client/server config files.
* Allow '@' character to be used in --client-config-dir
  file names.

2005.07.04 -- Version 2.0.1-rc5

* Windows version will log a for-further-info URL when
  initialization sequence is completed with errors.
* Added DLOPEN_PAM parameter to plugin/auth-pam/Makefile
  to control whether auth-pam plugin links to PAM via
  dlopen or -lpam.  By default, DLOPEN_PAM=1 so pre-existing
  behavior should be preserved.  DLOPEN_PAM=0 is the preferred
  setting to link via -lpam, but DLOPEN_PAM=1 works around
  a bug in SuSE 9.1 (and possibly other distros as well)
  where the PAM modules are not linked with -lpam.  See
  thread on openvpn-devel for more discussion about this
  patch (Simon Perreault).

2005.06.15 -- Version 2.0.1-rc4

* Support LZO 2.00, including changes to configure script to
  autodetect LZO version.

2005.06.12 -- Version 2.0.1-rc3

* Fixed a bug which caused standard file handles to not be closed
  after daemonization when --plugin and --daemon are used together,
  and if the plugin initialization function forks (as does auth-pam
  and down-root) (Simon Perreault).
* Added client-side up/down scripts in contrib/pull-resolv-conf
  for accepting server-pushed "dhcp-option DOMAIN" and "dhcp-option DNS"
  on Linux/Unix systems (Jesse Adelman).
* Fixed bug where if client-connect scripts/plugins were cascaded,
  and one (but not all) of them returned an error status, there might
  be cases where for an individual script/plugin, client-connect was
  called but not client-disconnect.  The goal of this fix is to
  ensure that if client-connect is called on a given client instance,
  then client-disconnect will definitely be called.  A potential
  complication of this fix is that when client-connect functions are
  cascaded, it's possible that the client-disconnect function would
  be called in cases where the related client-connect function returned
  an error status.  This fix should not alter OpenVPN behavior when
  scripts/plugins are not cascaded.
* Changed the hard-to-reproduce "Assertion failed at fragment.c:312"
  fatal error to a warning: "FRAG: outgoing buffer is not empty".
  Need more info on how to reproduce this one.
* When --duplicate-cn is used, the --ifconfig-pool allocation
  algorithm will now allocate the first available IP address.
* When --daemon and --management-hold are used together,
  OpenVPN will daemonize before it enters the management hold state.

2005.05.16 -- Version 2.0.1-rc2

* Modified vendor test in openvpn.spec file to match against
  "Mandrakesoft" in addition to "MandrakeSoft".
* Using --iroute in a --client-config-dir file while in --dev tap
  mode is not currently supported and will produce a warning
  message. Fixed bug where in certain cases, in addition to
  generating a warning message, this combination of options
  would also produce a fatal assertion in mroute.c.
* Pass --auth-user-pass username to server-side plugin without
  performing any string remapping (plugins, unlike scripts,
  don't get any security benefit from string remapping).
  This is intended to fix an issue with openvpn-auth-pam/pam_winbind
  where backslash characters in a username ('\') were being remapped
  to underscore ('_').
* Updated OpenSSL DLLs in Windows build to 0.9.7g.
* Documented --explicit-exit-notify in man page.
* --explicit-exit-notify seconds parameter defaults to 1 if
  unspecified.

2005.04.30 -- Version 2.0.1-rc1

* Fixed bug where certain kinds of fatal errors after
  initialization (such as port in use) would leave plugin
  processes (such as openvpn-auth-pam) still running.
* Added optional openvpn_plugin_abort_v1 plugin function for
  closing initialized plugin objects in the event of a fatal
  error by main OpenVPN process.
* When the --remote list is > 1, and --resolv-retry is not
  specified (meaning that it defaults to "infinite"), apply the
  infinite timeout to the --remote list as a whole, but try each
  list item only once before moving on to the next item.
* Added new --syslog directive which redirects output
  to syslog without requiring the use of the --daemon or --inetd
  directives.
* Added openvpn.spec option to allow RPM to be built with support
  for passwords read from a file:
  rpmbuild -tb [openvpn.x.tar.gz] --define 'with_password_save 1'

2005.04.17 -- Version 2.0

* Fixed minor options string typo in options.c.

2005.04.10 -- Version 2.0-rc21

* Change license description from "GPL Version 2 or (at your
  option) any later version" to just "GPL Version 2".

2005.04.04 -- Version 2.0-rc20

* Dag Wieers has put together an OpenVPN/LZO binary RPM set with
  excellent distro/version coverage for RH/EL/Fedora, though
  using his own SPEC.  I modified openvpn.spec to follow some of
  the same conventions such as putting sample scripts and doc
  files in %doc rather than /usr/share/openvpn.
* Minor change to init scripts to run the user-defined script
  /etc/openvpn/openvpn-startup (if it exists) before any OpenVPN
  configs are started, and to run /etc/openvpn/openvpn-shutdown
  after all OpenVPN configs have been stopped.  The
  openvpn-startup script can be used for stuff like
  insmod tun.o, setting up firewall rules, or starting
  ethernet bridges.

2005.03.29 -- Version 2.0-rc19

* Omit additions of routes where the network and
  gateway are equal and the netmask is ***************.
  This can come up if you are using both
  server/ifconfig-pool and client-config-dir with
  ifconfig-push static addresses for some subset of clients
  which directly reference the server IP address as the
  remote endpoint.
	
2005.03.28 -- Version 2.0-rc18

* Packaged Windows installer with OpenSSL 0.9.7f.
* Built Windows installer with NSIS 2.06.

2005.03.12 -- Version 2.0-rc17

* "MANAGEMENT: CMD" log file output will now only occur
  at --verb 7 or greater.
* Added an optional name/value configuration list to
  the openvpn-auth-pam plugin module argument list.  See
  plugin/auth-pam/README for documentation. This is necessary
  in order for openvpn-auth-pam to work with queries generated
  by arbitrary PAM modules.
* In both auth-pam and down-root plugins, in the forked process,
  a read error on the parent process socket is no longer fatal.
* MandrakeSoft liblzo1 RPM only Provides for a 'liblzo1'.
  A conditional test of the vendor has been added to
  Require the appropriately named 'lzo' (liblzo1 / lzo).
  (Tom Walsh - http://openhardware.net)

	
2005.02.20 -- Version 2.0-rc16

* Fixed bug introduced in rc13 where Windows service wrapper
  would be installed with a startup type of Automatic.
  This fix restores the previous behavior of installing
  with a startup type of Manual.

2005.02.19 -- Version 2.0-rc15

* Added warning when --keepalive is not used in a server
  configuration.
* Don't include OpenSSL md4.h file if we are not building
  NTLM proxy support (Waldemar Brodkorb).
* Added easy-rsa/build-key-pkcs12 and
  easy-rsa/Windows/build-key-pkcs12.bat scripts
  (Mathias Sundman).

2005.02.16 -- Version 2.0-rc14

* Fixed small memory leak that occurs when --crl-verify
  is used.
* Upgraded Windows installer and .nsi script to NSIS 2.05
  (Mathias Sundman).
* Changed #include backslash usage in cryptoapi.c to use
  forward slashes instead (Gisle Vanem).
* Created easy-rsa/revoke-full to handle revocations in
  a single step: (a) revoke crt, (b) regenerate CRL, and
  (c) verify that revocation succeeded.
* Renamed easy-rsa/Windows/revoke-key to revoke-full so
  that both *nix and Windows scripts are equivalent.
	
2005.02.11 -- Version 2.0-rc13

* Improve human-readability of local/remote options
  diff, when inconsistencies are present.
* For Windows easy-rsa, distribute vars.bat.sample and
  openssl.cnf.sample, then copy them to their normal
  filenames (without the .sample) when init-config.bat
  is run.  This is to prevent OpenVPN upgrades from
  wiping out vars.bat and openssl.cnf edits.
* Modified service wrapper (Windows) to use a
  case-insensitive search when scanning for .ovpn files
  in \Program Files\OpenVPN\config.  Prior versions
  required an all-lower-case .ovpn file extension.
* Miscellaneous service wrapper code cleanup.
* If --user/--group is used on Windows, treat it
  as a no-op with a warning (this makes it easier to
  distribute the same client config file to Windows
  and *nix users).
* Warn if --ifconfig-pool-persist is used with
  --duplicate-cn.

2005.02.05 -- Version 2.0-rc12

* Removed some debugging code inadvertently included
  in rc11 which would print the --auth-user-pass
  username/password provided by clients in the server
  logfile.
* Client code for cycling through --remote list will
  retry the last address which successfully authenticated
  before moving on through the list.
* Windows installer will now install sample configuration
  files in \Program Files\OpenVPN\sample-configs as well
  as generate a start menu shortcut to this directory.
* Minor type change in buffer.[ch] to work around char-type
  ambiguity bug.  Caused management interface lock-ups on
  ARM when building with armv4b-hardhat-linux-gcc 2.95.3.

2005.02.03 -- Version 2.0-rc11

* Windows installer will now install easy-rsa directory
  in \Program Files\OpenVPN
* Allow syslog facility to be controlled at compile time,
  e.g. -DLOG_OPENVPN=LOG_LOCAL6 (P Kern).
* Changed certain shell scripts in distribution to use
  #!/bin/sh rather than #!/bin/bash for better portability.
* If --ifconfig-pool-persist seconds parameter is 0, treat
  persist file as an allocation of fixed IP addresses
  (previous versions took IP-to-common-name associations
  from this list as hints, not mandatory static allocations).
* Fixed bug on *nix where if --auth-user-pass and --log
  were used together, the username prompt would be sent to
  the log file rather than /dev/tty.
* Spurious text in openvpn.8 detected by doclifter
  (Eric S. Raymond).
* Call closelog later on daemon kill so that process
  exit message is written to syslog.

2005.01.27 -- Version 2.0-rc10

* When ./configure is run with plugins enabled (the default),
  check whether or not dlopen exists in libc before testing
  for libdl.  This is to fix an issue on FreeBSD and possibly
  other OSes which bundle libdl functions in libc.
* On Windows, filter initial WSAEINVAL warning which occurs
  on the initial read attempt of an unbound socket.
* The easy-rsa scripts build-key, build-key-pass, and
  build-key-server will now chmod the .key file
  to 0600.  This is in addition to the fact the generated
  keys directory has always been similarly protected
  (Pete Harlan).

2005.01.23 -- Version 2.0-rc9

* Fixed error "ROUTE: route addition failed using
  CreateIpForwardEntry ..." on Windows when --redirect-gateway
  is used over a RRAS internet link.
* When using --route-method exe on Windows, include the
  gateway parameter on route delete commands (Mathias Sundman).
* Try not to do a hard reset (i.e. SIGHUP) when two
  SIGUSR1 signals are received in close succession.
* If the push list tries to grow beyond its buffer capacity,
  the resulting error will be non-fatal.
* To increase the push list capacity (must be done on both
  client and server), increase TLS_CHANNEL_BUF_SIZE in
  common.h (default=1024).
	
2005.01.15 -- Version 2.0-rc8

* Fixed bug introduced in rc7 where options error
  "--auth-user-pass requires --pull" might occur even
  if --pull was correctly specified.
* Changed management interface code to bind once
  to TCP socket, rather than rebinding after every
  client disconnect.
* Added "disable" directive for client-config-dir
  files.
* Windows binary install is now distributed with
  OpenSSL 0.9.7e.
* Query the management interface for --http-proxy
  username/password if authfile is set to "stdin".
* Added current OpenVPN version number to "Unrecognized
  option or missing parameter" error message.
* Added "-extensions server" to "openssl req" command
  in easy-rsa/build-key-server (Nir Yeffet).
	
2005.01.10 -- Version 2.0-rc7

* Fixed bug in management interface which could cause
  100% CPU utilization in --proto tcp-server mode
  on all *nix OSes except for Linux 2.6.
* --ifconfig-push now accepts DNS names as well as
  IP addresses.
* Added sanity check errors when --pull or
  --auth-user-pass is used in an incorrect mode.
* Updated man page entries for --client-connect and
  --ifconfig-push.
* Added "String Types and Remapping" section to man
  page to consisely document the way which OpenVPN
  may convert certain types of characters in strings
  to ('_').
* Modified bridging description in HOWTO to emphasize
  the fact that bridging allows Windows file and print
  sharing without a WINS server (Charles Duffy).

2004.12.20 -- Version 2.0-rc6

* Improved checking for epoll support in ./configure
  to fix false positive on RH9 (Jan Just Keijser).
* Made the "MULTI TCP: I/O wait required blocking in
  multi_tcp_action, action=7" error nonfatal and replaced
  with "MULTI: Outgoing TUN queue full, dropped packet".
  So far the issue only seems to occur on Linux 2.2
  in --mode server --proto tcp mode.  It occurs when
  the TUN/TAP driver locks up and refuses to accept
  new packet writes for a second or more.
* Fixed bug where if a --client-config-dir file tried
  to include another file using "config", and if that
  include failed, OpenVPN would abort with a fatal
  error.  Now such inclusion failures will be logged
  but are no longer fatal.
* Global changes to the way that packet buffer alignment
  is handled.  Previously we didn't care about alignment
  and took care, when handling 16 and 32 bit words
  in buffers, to always use alignment-safe transfers.
  This approach appears to be inadequate on some
  architectures such as alpha.  The new approach is
  to initialize packet buffers in a way that anticipates
  how component structures will be allocated within
  them, to maintain correct alignment.
* Added --dhcp-option DISABLE-NBT to disable NetBIOS
  over TCP (Jan Just Keijser).
* Added --http-proxy-option directive for controlling
  miscellaneous HTTP proxy options.
* Management state will no longer transition to "WAIT"
  during TLS renegotiations.

2004.12.16 -- Version 2.0-rc5

* The --client-config-dir option will now try to open
  a default file called "DEFAULT" if no file matching
  the common name of the incoming client was found.
* The --client-connect script/plugin can now veto client
  authentication by returning a failure code.
* The --learn-address script/plugin can now prevent a
  client-instance/address association from being learned
  by returning a failure code.
* Changed RPM group in .spec file to Applications/Internet.

2004.12.14 -- Version 2.0-rc4

* SuSE only -- Fixed interaction between openvpn.spec and
  suse/openvpn.init where the .spec file was writing the
  OpenVPN binary to a different location than where the
  .init script was referencing it (Stefan Engel).
* Solaris only -- Split Solaris ifconfig command into two
  parts (Jan Just Keijser).
* Some cleanup in add_option().
* Better error checking on input dotted quad IP addresses.
* Verify that --push argument is quoted, if there is
  more than one.
* More miscellaneous option sanity checks.

2004.12.13 -- Version 2.0-rc3

* On Windows, when --log or --log-append is used,
  save the original stderr for username and password
  prompts.
* Fixed a bug introduced in the late 2.0 betas where
  if a "verb" parameter >= 16 was used, it would be
  ignored and the actual verb level would remain at 1.
* Fixed a bug mostly seen on OS X where --management-hold
  or --management-query-passwords would cause the management
  interface to be unresponsive to incoming client connections.
* Trigger an options error if one of the management-modifying
  options is used without "management" itself.

2004.12.12 -- Version 2.0-rc2

* Amplified warnings in documentation about possible
  man-in-the-middle attack when clients do not properly
  verify server certificate.  Changes to easy-rsa README,
  FAQ, HOWTO, man page, and sample client config file.
* Added a warning message if --tls-client or --client
  is used without also specifying one of either
  --ns-cert-type, --tls-remote, or --tls-verify.
* status_open() fixes for MSVC builds (Blaine Fleming).
* Fix attempt of "ntlm.c:55: error: `des_cblock' undeclared"
  compiler error which has been reported on some platforms.
* The openvpn.spec file for rpmbuild has several
  new build-time options.  See comments in the file.
* Plugins are now built and packaged in the RPM and
  will be saved in /usr/share/openvpn/plugin/lib.
* Added --management-hold directive to start OpenVPN
  in a hibernating state until released by the
  management interface.  Also added "hold" command
  to the management interface.
 	
2004.12.07 -- Version 2.0-rc1

* openvpn.spec workaround for SuSE confusion regarding
  /etc/init.d vs. /etc/rc.d/init.d (Stefan Engel).

2004.12.05 -- Version 2.0-beta20

* The ability to read --askpass and --auth-user-pass
  passwords from a file has been disabled by default.
  To re-enable, use ./configure --enable-password-save.
* Added additional pre-connected states to management
  interface.  See management/management-notes.txt
  for more info.
* State history is now recorded by the management
  interface, and the "state" command now works like
  the log or echo commands.
* State history and real-time state change notifications
  are now prepended with an integer unix timestamp.
* Added --http-proxy-timeout option, previously
  the timeout was hardcoded to 5 seconds.
	
2004.12.02 -- Version 2.0-beta19

* Fixed bug in management interface line termination
  where output lines incorrectly contained a \00 char
  after the customary \0d \0a.
* Fixed bug introduced in beta18 where Windows version
  would segfault on options errors.
* Fixed bug in management interface where an empty
  quoted string ("") entered as a parameter would cause
  a segfault.
* Fixed bug where --resolv-retry was not working
  properly with multiple --remote hosts.
* Added additional ./configure options to reduce
  executable size for embedded applications.
  See ./configure --help.

2004.11.28 -- Version 2.0-beta18

* Added management interface.  See new --management-*
  options or the full management interface documentation
  in management/management-notes.txt in the tarball.
  Management interface inclusion can be disabled by
  ./configure --disable-management.
* Added two new plugin modules: auth-pam and down-root.
  Auth-pam supports pam-based authentication using a
  split privilege execution model, while down-root enables
  a down script to be executed with root privileges, even
  when --user/--group is used to drop root privileges.
  See the plugin directory in the tarball for READMEs,
  source code, and Makefiles.
* Plugin developers should note that some changes were
  made to the plugin interface since beta17.  See
  openvpn-plugin.h for details.
  Plugin interface inclusion can be disabled with
  ./configure --disable-plugins
* Added easy-rsa/build-key-server script which will
  build a certificate with with nsCertType=server.
* Added --ns-cert-type option for verification
  of nsCertType field in peer certificate.
* If --fragment n is specified and --mssfix is specified
  without a parameter, default --mssfix to n.  This restores
  the 1.6 behavior when using --mssfix without a parameter.
* Fixed SSL context initialization bug introduced in beta14
  where this error might occur on restarts: "Cannot load
  certificate chain ... PEM_read_bio:no start line".

2004.11.11 -- Version 2.0-beta17

* Changed default port number to 1194 per IANA official
  port number assignment.
* Added --plugin directive which allows compiled
  modules to intercept script callbacks.  See
  plugin folder in tarball for more info.
* Fixed bug introduced in beta12 where --key-method 1
  authentications which should have succeeded would fail.
* Ignore SIGUSR1 during DNS resolution.
* Added SuSE support to openvpn.spec (Umberto Nicoletti).
* Fixed --cryptoapicert SUBJ: parsing bug (Peter 'Luna'
  Runestig).

2004.11.07 -- Version 2.0-beta16

* Modified sample-scripts/auth-pam.pl to get username
  and password from OpenVPN via a file rather than
  via environmental variables.
* Added bytes_sent and bytes_received environmental
  variables to be set prior to client-disconnect script.
* Changed client virtual IP derivation precedence:
  (1) use --ifconfig-push directive from --client-connect
  script, (2) use --ifconfig-push directive from
  --client-config-dir, and (3) use --ifconfig-pool
  address.
* If a --client-config-dir file specifies --ifconfig-push,
  it will be visible to the --client-connect-script in
  the ifconfig_pool_remote_ip environmental variable.
* For tun-style tunnels, the ifconfig_pool_local_ip
  environmental variable will be set, while for
  tap-style tunnels, the ifconfig_pool_netmask variable
  will be set.
* Added intelligence to autoconf script to test
  compiler for the accepted form of zero-length arrays.
* Fixed a bug introduced in beta12 where --ip-win32
  netsh would fail if --dev-node was not explicitly
  specified.
* --ip-win32 netsh will now work on hidden adapters.
* Fix attempt of "Assertion failed at crypto.c:149".
  This assertion has also been reported on 1.x with a
  slightly different line number.  The fix is twofold:
  (1) In previous releases, --mtu-test may trigger this
  assertion -- this bug has been fixed.  (2) If something
  else causes the assertion to be thrown, don't panic,
  just output a nonfatal warning to the log and drop
  the packet which generated the error.
* Support TAP interfaces on Mac OS X (Waldemar Brodkorb).
* Added --echo directive.
* Added --auth-nocache directive.

2004.10.28 -- Version 2.0-beta15

* Changed environmental variable character classes
  so that names must consist of alphanumeric or
  underbar chars and values must consist of printable
  characters.  Illegal chars will be deleted.
  Versions prior to 2.0-beta12 were more restrictive
  and would map spaces to '.'.
* On Windows, when the TAP adapter fails to
  initialize with the correct IP address, output
  "Initialization Sequence Completed with Errors"
  to the console or log file.
* Added a warning when user/group/chroot is used
  without persist-tun and persist-key.
* Added cryptoapi.[ch] to tarball and source zip.
* --tls-remote option now works with common name
  prefixes as well as with the full X509 subject
  string.  This is a useful alternative to using
  a CRL on the client.
* common names associated with a static
  --ifconfig-push setting will no longer leave
  any state in the --ifconfig-pool-persist file.
* Hard TLS errors (TLS handshake failed) will now
  trigger either a SIGUSR1 signal by default
  or SIGTERM (if --tls-exit is specified).  In TCP
  mode, all TLS errors are considered to be hard.
  In server mode, the signal will be local to the
  client instance.
* Added method parameter to --auth-user-pass-verify
  directive to select whether username/password
  is passed to script via environment or a temporary
  file.
* Added --status-version option to control format
  of --status file.  The --mode server
  --status-version 2 format now includes a line
  type token, the virtual IP address is shown
  in the client list (even in --dev tap mode),
  and the integer time_t value is shown anywhere
  an ascii-formatted time/date is also shown.
* Added --remap-usr1 directive which can be used
  to control whether internally or externally
  generated SIGUSR1 signals are remapped to
  SIGHUP (restart without persisting state) or
  SIGTERM (exit).
* When running as a Windows service (using
  --service option), check the exit event before
  and after reading one line of input from
  stdin, when reading username/password info.
* For developers: Extended the --gremlin function
  to better stress-test the new 2.0 features,
  added Valgrind support on Linux and Dmalloc
  support on Windows.

2004.10.19 -- Version 2.0-beta14

* Fixed a bug introduced in Beta12 that would occur
  if you use a --client-connect script without also
  defining --tmp-dir.
* Fixed a bug introduced in Beta12 where a learn-address
  script might segfault on the delete method.
* Added Crypto API support in Windows version via
  the --cryptoapicert option (Peter 'Luna' Runestig).

2004.10.18 -- Version 2.0-beta13

* Fixed an issue introduced in Beta12 where the private
  key password would not be prompted for unless --askpass
  was explicitly specified in the config.

2004.10.17 -- Version 2.0-beta12

* Added support for username/password-based authentication.
  Clients can now authentication themselves with the server
  using either a certificate, a username/password, or both.
  New directives: --auth-user-pass, --auth-user-pass-verify,
  --client-cert-not-required, and --username-as-common-name.
* Added NTLM proxy patch (William Preston).
* Added --ifconfig-pool-linear server flag to allocate
  individual tun addresses for clients rather than /30
  subnets (won't work with Windows clients).
* Modified --http-proxy code to cache username/password
  across restarts.
* Modified --http-proxy code to read username/password
  from the console when the auth file is given as "stdin".
* Modified --askpass to take an optional filename argument.
* --persist-tun and --persist-key now work in client mode
  and can be pushed to clients as well.
* Added --ifconfig-pool-persist directive, to maintain
  ifconfig-pool info in a file which is persistent across
  daemon instantiations.
* --user and --group privilege downgrades as well as
  --chroot now also work in client mode (the
  dowgrade/chroot will be delayed until the initialization
  sequence is completed).
* Added --show-engines standalone directive to show
  available OpenSSL crypto accelerator engine support.
* --engine directive now accepts an optional engine-ID
  parameter to control which engine is used.
* "Connection reset, restarting" log message now shows
  which client is being reset.
* Added --dhcp-pre-release directive in Windows version.
* Second parm to --ip-win32 can be "default", e.g.
  --ip-win32 dynamic default 60.
* Fixed documentation bug regarding environmental
  variable settings for --ifconfig-pool IP addresses.
  The correct environmental variable names are:
  ifconfig_pool_local_ip and ifconfig_pool_remote_ip.
* ifconfig_pool_local_ip and ifconfig_pool_remote_ip
  environmental variables are now passed to the
  client-disconnect script.
* In server mode, environmental variables are now scoped
  according to the client they are associated with,
  to solve the problem of "crosstalk" between different
  client's environmental variable sets.
* Added --down-pre flag to cause --down script to be
  called before TUN/TAP close (rather than after).
* Added --tls-exit flag which will cause OpenVPN
  to exit on any TLS errors.
* Don't push a route to a client if it exactly
  matches an iroute (this lets you push routes to
  all clients, and OpenVPN will automatically remove
  the route from the route push list only for that client
  which the route actually belongs to).
* Made '--resolv-retry infinite' the default.
  --resolv-retry can be disabled by using a parameter of 0.
* For clients which plan to pull config info from server,
  set an initial default ping-restart of 60 seconds.
* Optimized mute code to lessen the load on the processor
  when messages are being muted at a higher frequency.
* Made route log messages non-mutable.
* Silence the Linux "No buffer space available" message.
* Added miscellaneous additional option sanity checks.
* Added Windows version of easy-rsa scripts in
  easy-rsa/Windows directory (Andrew J. Richardson).
* Added NetBSD route patch (Ed Ravin).
* Added OpenBSD patch for TAP + --redirect-gateway
  (Waldemar Brodkorb).
* Directives which prompt for a username and/or password
  will now work with --daemon (OpenVPN will prompt
  before forking).
* Warn if CRL is from a different issuer than the
  issuer of the peer certificate (Bernhard Weisshuhn).
* Changed init script chkconfig parameters to start
  OpenVPN daemon(s) before NFS.
* Bug fix attempt of "too many I/O wait events" which occurs
  on OSes which prefer select() over poll() such as Mac OS X.
* Added --ccd-exclusive flag.  This flag will require, as a
  condition of authentication, that a connecting client has
  a --client-config-dir file.
* TAP-Win32 open code will attempt to open a free adapter
  if --dev-node is not specified (Mathias Sundman).
* Resequenced --nice and --chroot ordering so that --nice
  occurs first.
* Added --suppress-timestamps flag (Charles Duffy).
* Source code changes to allow compilation by MSVC
  (Peter 'Luna' Runestig).
* Added experimental --fast-io flag which optimizes
  TUN/TAP/UDP writes on non-Windows systems.
	
2004.08.18 -- Version 2.0-beta11

* Added --server, --server-bridge, --client, and
  --keepalive helper directives.  See client.conf
  and server.conf in sample-config-files for sample
  configurations which use the new directives.
* On Windows, added --route-method to control
  whether IP Helper API or route.exe is used
  to add/delete routes.
* On Windows, added a second parameter to
  --route-delay to control the maximum time period
  to wait for the TAP-Win32 adapter to come up
  before adding routes.
* Fixed bug in Windows version where configurations
  which omit --ifconfig might fail to recognize when
  the TAP adapter is up.
* Proxy connection failures will now retry according
  to the --connect-retry parameter. 
* Fixed --dev null handling on Windows so that TLS
  loopback test described in INSTALL file works
  correctly on Windows.
* Added "Initialization Sequence Completed" message
  after all initialization steps have been completed
  and the VPN can be considered "up".
* Better sanity-checking on --ifconfig-pool parameters.
* Added --tcp-queue-limit option to control
  TUN/TAP -> TCP socket overflow.
* --ifconfig-nowarn flag will now silence general
  warnings about possible --ifconfig address
  conflicts, including the warning about --ifconfig
  and --remote addresses being in same /24 subnet.
* Fixed case where server mode did not correctly
  identify certain types of ethernet multicast packets
  (Marcel de Kogel).
* Added --explicit-exit-notify option (experimental).

2004.08.02 -- Version 2.0-beta10

* Fixed possible reference after free of option strings
  after a restart, bug was introduced in beta8.
* Fixed segfault at route.c:919 in the beta9
  Windows version that was being caused by indirection
  through a NULL pointer.
* Mistakenly built debug version of TAP-Win32 driver
  for beta9.  Beta10 has correct release build.

2004.07.30 -- Version 2.0-beta9

* Fixed --route issue on Windows that was introduced with
  the new beta8 route implementation based on the
  IP Helper API.

2004.07.27 -- Version 2.0-beta8

* Added TCP support in server mode.
* Added PKCS #12 support (Mathias Sundman).
* Added patch to make revoke-crt and make-crl work
  seamlessly within the easy-rsa environment (Jan Kiszka).
* Modified --mode server ethernet bridge code to forward
  special IEEE 802.1d MAC Groups, i.e. 01:80:C2:XX:XX:XX.
* Added --dhcp-renew and --dhcp-release flags to Windows
  version.  Normally DHCP renewal and release on the TAP
  adapter occurs automatically under Windows, however
  if you set the TAP-Win32 adapter Media Status property
  to "Always Connected", you may need these flags.
* Added --show-net standalone flag to Windows version to
  show OpenVPN's view of the system adapter and routing
  tables.
* Added --show-net-up flag to Windows version to output
  the system routing table and network adapter list to
  the log file after the TAP-Win32 adapter has been brought
  up and any routes have been added.
* Modified Windows version to add routes using the IP Helper
  API rather than by calling route.exe.
* Fixed bug where --route-up script was not being called
  if no --route options were specified.
* Added --mute-replay-warnings to suppress packet replay
  warnings.  This is a common false alarm on WiFi nets.
* Added "def1" flag to --redirect-gateway option to override
  the default gateway by using 0.0.0.0/1 and *********/1
  rather than 0.0.0.0/0.  This has the benefit of overriding
  but not wiping out the original default gateway.
  (Thanks to Jim Carter for pointing out this idea).
* You can now run OpenVPN with a single config file argument.
  For example, you can now say "openvpn config.conf"
  rather than "openvpn --config config.conf".
* On Windows, made --route and --route-delay more adaptive
  with respect to waiting for interfaces referenced by the
  route destination to come up.  Routes added by --route
  should now be added as soon as the interface comes up,
  rather than after an obligatory 10 second delay.  The
  way this works internally is that --route-delay now
  defaults to 0 on Windows.  Previous versions would
  wait for --route-delay seconds then add the routes.
  This version will wait --route-delay seconds and then
  test the routing table at one second intervals for the
  next 30 seconds and will not add the routes until they
  can be added without errors.
* On Windows, don't setsockopt SO_SNDBUF or SO_RCVBUF by
  default on TCP/UDP socket in light of reports that this
  action can have undesirable global side effects on the
  MTU settings of other adapters.  These parameters can
  still be set, but you need to explicitly specify
  --sndbuf and/or --rcvbuf.
* Added --max-clients option to limit the maximum number
  of simultaneously connected clients in server mode.
* Added error message to illuminate shell escape gotcha when
  single backslashes are used in Windows path names.
* Added optional netmask parm to --ifconfig-pool.
* Fixed bug where http-proxy connect retry attempts were
  incorrectly going to the remote OpenVPN server,
  not to the HTTP proxy server.

2004.06.29 -- Version 2.0-beta7

* Fixed bug in link_socket_verify_incoming_addr() which
  under certain circumstances could have caused --float
  behavior even if --float was not specified.
* --tls-auth option now works with --mode server.
  All clients and the server should use the same
  --tls-auth key when operating in client/server mode.
* Added --engine option to make use of OpenSSL-supported
  crypto acceleration hardware.
* Fixed some high verbosity print format size issues
  in event.c for 64 bit platforms (Janne Johansson).
* Made failure to open --log or --log-append file
  a non-fatal error.

2004.06.23 -- Version 2.0-beta6

* Fixed Windows installer to intelligently put
  up a reboot dialog only if tapinstall tells
  us that it's really necessary.
* Fixed "Assertion failed at fragment.c:309"
  bug when --mode server and --fragment are used
  together.
* Ignore HUP, USR1, and USR2 signals during
  initialization.  Prior versions would abort.
* Fixed bug on OS X: "Assertion failed at event.c:406".
* Added --service option to Windows version, for use
  when OpenVPN is being programmatically instantiated
  by another process (see man page for info).
* --log and --log-append options now work on Windows.
* Update OpenBSD INSTALL notes (Janne Johansson).
* Enable multicast on tun interface when running on
  OpenBSD (Pavlin Radoslavov).
* Fixed recent --test-crypto breakage, where options
  such as --cipher were not being parsed correctly.
* Modified options compatibility string by removing
  ifconfig substring if it is empty.  Incremented
  options compatibility string version number to 4.
* Fixed typo in --tls-timeout option parsing
  (Mikael Lonnroth).

2004.06.13 -- Version 2.0-beta5

* Fixed rare --mode server crash that could occur
  if data was being routed to a client at
  high bandwidth at the precise moment that the
  client instance object on the server was being
  deleted.
* Fixed issue on machines which have epoll.h and
  the epoll_create glibc call defined, but which
  don't actually implement epoll in the kernel.
  OpenVPN will now gracefully fall back to the
  poll API in this case.
* Fixed Windows bug which would cause the following
  error in a --mode server --dev tap configuration:
  "resource limit WSA_MAXIMUM_WAIT_EVENTS has been
  exceeded".
* Added CRL (certificate revocation list) management
  scripts to easy-rsa directory (Jon Bendtsen).
* Do a better job of getting the ifconfig component
  of the options consistency check to work correctly
  when --up-delay is used.
* De-inlined some functions which were too complex
  to be inlined anyway with gcc.
* If a --dhcp-option option is pushed to a non-windows
  client, the option will be saved in the client's
  environment before the --up script is called, under
  the name "foreign_option_{n}".
* Added --learn-address script (see man page) which
  allows for firewall access through the VPN to be
  controlled based on the client common name.
* In mode --server mode, when a client connects to
  the server, the server will disconnect any
  still-active clients which use the same common
  name.  Use --duplicate-cn flag to revert to
  previous behavior of allowing multiple clients
  to concurrently connect with the same common name.

2004.06.08 -- Version 2.0-beta4

* Fixed issue with beta3 where Win32 service wrapper
  was keying off of old TAP HWID as a dependency.  To
  ensure that the new service wrapper is correctly
  installed, the Windows install script will uninstall
  the old wrapper before installing the new one,
  causing a reset of service properties.
* Fixed permissions issue on --status output file,
  with default access permissions of owner read/write
  only (default permissions can be changed of course with
  chmod).

2004.06.05 -- Version 2.0-beta3

* More changes to TAP-Win32 driver's INF file which
  affects the placement of the driver in the Windows
  device namespace.  This is done to work around an
  apparent bug in Windows when short HWIDs are used,
  and will also ease the upgrade from 1.x to 2.0 by
  reducing the chances that a reboot will be needed
  on upgrade.  Like beta2, this upgrade will
  delete existing TAP-Win32 interfaces, and reinstall
  a single new interface with default properties.
* Major rewrite of I/O event wait layer in the style
  of libevent.  This is a precursor to TCP support
  in --mode server.
* New feature: --status.  Outputs a SIGUSR2-like
  status summary to a given file, updated once
  per n seconds.  The status file is comma delimited
  for easy machine parsing.
* --ifconfig-pool now remembers common names and
  will try to assign a consistent IP to a given
  common name.  Still to do: persist --ifconfig-pool
  memory across restarts by saving state in file.
* Fixed bug in event timer queue which could cause
  recurring timer events such as --ping to not
  correctly schedule again after firing.  This in
  turn would cause spurrious ping restarts and possible
  connection outages.  Thanks to Denis Vlasenko for
  tracking this down.
* Possible fix to reported bug where --daemon argument
  was not printing to syslog correctly after restart.
* Fixed bug where pulling --route or --dhcp-option
  directives from a server would problematically
  interact with --persist-tun on the client.
* Updated contrib/multilevel-init.patch (Farkas Levente).
* Added RPM build option to .spec and .spec.in files
  to optionally disable LZO inclusion (Ian Pilcher).
* The latest MingW runtime and headers define
  'ssize_t', so a patch is needed (Gisle Vanem).

2004.05.14 -- Version 2.0-beta2

* Fixed signal handling bug in --mode server, where
  SIGHUP and SIGUSR1 were treated as SIGTERM.
* Changed the TAP-Win32 HWID from "TAP" to "TAPDEV".
  Apparently the larger string may work around
  a problem where the TAP adapter is sometimes missing
  from the network connections panel, especially under
  XP SP2. Also note that installing this upgrade will
  uninstall any pre-existing TAP-Win32 adapters, and then
  install a single new adapter, meaning that old adapter
  properties will be lost.  Thanks to Md5Chap for solving
  this one.
* For --mode server --dev tap, the options --ifconfig and
  --ifconfig-pool are now optional.  This allows address
  assignment via DHCP or use of a TAP VPN without
  IP support, as has always been possible with 1.x.
* Fixed bug where --ifconfig may not work correctly on
  Linux 2.2.
* Added 'local' flag to --redirect-gateway for use on
  networks where both OpenVPN daemons are connected
  to a shared subnet, such as wireless.

2004.05.09 -- Version 2.0-beta1

* Unchanged from test29 except for version number
  upgrade.

2004.05.08 -- Version 2.0-test29

* Modified --dev-node on Windows to accept a TAP-Win32
  GUID name.  In addition, --show-adapters will now
  display the high-level name and GUID of each adapter.
  This is an attempt to work around an issue in Windows
  where sometimes the TAP-Win32 adapter installs correctly
  but has no icon in the network connections control
  panel.  In such cases, being able to specify
  --dev-node {TAP-GUID} can work around the missing icon.

2004.05.07 -- Version 2.0-test28

* Fixed bug which could cause segfault on program
  shutdown if --route and --persist-tun are used
  together.

2004.05.06 -- Version 2.0-test27

* Fixed bug in close_instance() which might cause
  memory to be accessed after it had already been freed.
* Fixed bug in verify_callback() that might have
  caused uninitialized data to be referenced.
* --iroute now allows full CIDR subnet routing.
* In "--mode server --dev tun" usage, source addresses
  on VPN packets coming from a particular client must
  be associated with that client in the OpenVPN internal
  routing table.
	
2004.04.28 -- Version 2.0-test26

* Optimized broadcast path in multi-client mode.
* Added socket buffer size options --rcvbuf & --sndbuf.
* Configure Linux tun/tap driver to use a more sensible
  txqueuelen default.  Also allow explicit setting
  via --txqueuelen option (Harald Roelle).
* The --remote option now allows the port number
  to be specified as the second parameter.  If
  unspecified, the port number defaults to the
  --rport value.
* Multiple --remote options on the client can now be
  specified for load balancing and failover.  The
  --remote-random flag can be used to initially randomize
  the --remote list for basic load balancing.
* If a remote DNS name resolves to multiple DNS addresses,
  one will be chosen by random as a kind of basic
  load-balancing feature if --remote-random is used.
* Added --connect-freq option to control maximum
  new connection frequency in multi-client mode.
* In multi-client mode, all syslog messages associated
  with a specific client now include a client-ID prefix.
* For Windows, use a gettimeofday() function based
  on QueryPerformanceCounter (Derek Burdick).
* Fixed bug in interaction between --key-method 2
  and DES ciphers, where dynamic keys would be generated
  with bad parity and then be rejected.

2004.04.17 -- Version 2.0-test24

* Reworked multi-client broadcast handling.

2004.04.13 -- Version 2.0-test23
	
* Fixed bug in --dev tun --client-to-client routing.
* Fixed a potential deadlock in --pull.
* Fixed a problem with select() usage which could
  cause a repeating sequence of "select : Invalid
  argument (code=22)"

2004.04.11 -- Version 2.0-test22

* Fixed bug where --mode server + --daemon was
  prematurely closing syslog connection.
* Added support for --redirect-gateway on Mac OS X
  (Jeremy Apple).
* Minor changes to TAP-Win32 driver based on feedback
  from the NDISTest tool.
	
2004.04.11 -- Version 2.0-test21

* Optimizations in multi-client server event loop.

2004.04.10 -- Version 2.0-test20

* --mode server capability now works with either tun
  or tap interfaces.  When used with tap interfaces,
  OpenVPN will internally bridge all client tap
  interfaces with the server tap interface.
* Connecting clients can now have a client-specific
  configuration on the server, based on the client
  common name embedded in the client certificate.
  See --client-config-dir and --client-connect.
  These options can be used to configure client-specific
  routes.
* Added an option --client-to-client that enables
  internal client-to-client routing or bridging.
  Otherwise, clients will only "see" the server,
  not other connected clients.
* Fixed bug in route scheduling which would have caused
  --mode server to not work on Windows in test18
  and test19 with the sample config file.
* Man page is up to date with all new options.
* OpenVPN 2.0 release notes on web site updated
  with tap-style tunnel examples.

2004.04.02 -- Version 2.0-test19

* Fixed bug where routes pushed from server were
  not working correctly on Windows clients.
* Added Mac OS X route patch (Jeremy Apple).

2004.03.30 -- Version 2.0-test18

* Minor fixes + Windows self-install modified
  to use OpenSSL 0.9.7d.

2004.03.29 -- Version 2.0-test17

* Fixed some bugs related to instance timeout and deletion.
* Extended --push/--pull option to support additional
  option classes.

2004.03.28 -- Version 2.0-test16

* Successful test of --mode udp-server, --push,
  --pull, and --ifconfig-pool with server on
  Linux 2.4 and clients on Linux and Windows.

2004.03.25 -- Version 2.0-test15
	
* Implemented hash-table lookup of client instances
  based either on remote UDP address/port or remote
  ifconfig endpoint.
* Implemented a randomized binary tree based
  scheduler for scalably scheduling a large number
  of client instance events.  Uses the treap
  data structure and node rotation algorithm
  to keep the tree balanced.
* Initial implementation of ifconfig-pool.
* Made --key-method 2 the default.

2004.03.20 -- Version 2.0-test14

* Implemented --push and --pull.

2004.03.20 -- Version 2.0-test13

* Reduced struct tls_multi and --single-session
  memory footprint.
* Modified --single-session flag to be used
  in multi-client UDP server client instances.

2004.03.19 -- Version 2.0-test12

* Added the key multi-client UDP server options,
  --mode, --push, --pull, and --ifconfig-pool.
* Revamped GC (garbage collection) code to not rely
  on any global data.
* Modifications to thread.[ch] to allow a more
  flexible thread model.

2004.03.16 -- Version 2.0-test11

* Moved all timer code to interval.h, added new file
  interval.c.
* Fixed missing include.

2004.03.16 -- Version 2.0-test10

* More TAP-Win32 fixes.
* Initial debugging and testing of multi.[ch].

2004.03.14 -- Version 2.0-test9

* Branch merge with 1.6-rc3
* More point-to-multipoint work in multi.[ch].
* Major TAP-Win32 driver restructuring to use
  NdisMRegisterDevice instead of
  IoCreateDevice/IoCreateSymbolicLink.
* Changed TAP-Win32 symbolic links to use \DosDevices\Global\
  pathname prefix.
* In the majority of cases, TAP-Win32 should now be
  able to install and uninstall on Win2K without requiring
  a reboot.
* TAP-Win32 MAC address can now be explicitly set in the
  adapter advanced properties page.

2004.03.04 -- Version 2.0-test8

* Branch merge with 1.6-rc2.

2004.03.03 -- Version 2.0-test7

* Branch merge with 1.6-rc1.2.

2004.03.02 -- Version 2.0-test6

* Branch merge with 1.6-rc1.

2004.03.02 -- Version 2.0-test5

* Move Socks5 UDP header append/remove to socks.c, and is
  called from forward.c.
* Moved verify statics from ssl.c into struct tls_session.
* Wrote multi.[ch] to handle top level of point-to-multipoint
  mode.
* Wrote some code to allow a struct link_socket in a child context
  to be slaved to the parent context.
* Broke up packet read and process functions in forward.c
  (from socket or tuntap) into separate functions for read
  and process, so that point-to-point and point-to-multipoint can
  share the same code.
* Expand TLS control channel to allow the passing of configuration
  commands.
* Wrote mroute.[ch] to handle internal packet routing for
  point-to-multipoint mode.

2004.02.22 -- Version 2.0-test3

* Initial work on UDP multi-client server.
* Branch merge of 1.6-beta7
	
2004.02.14 -- Version 2.0-test2

* Refactorization of openvpn.c into openvpn.[ch]
  init.[ch] forward.[ch] forward-inline.h
  occ.[ch] occ-inline.h  ping.[ch] ping-inline.h
  sig.[ch].  Created a master per-tunnel
  struct context in openvpn.h.
* Branch merge of 1.6-beta6.2

2003.11.06 -- Version 2.0-test1

* Initial testbed for 2.0.

2004.05.09 -- Version 1.6.0
	
* Unchanged from 1.6-rc4 except for version number
  upgrade.

2004.04.01 -- Version 1.6-rc4

* Made minor customizations to devcon and
  renamed as tapinstall.exe for Windows version.
* Fixed "storage size of `iv' isn't known" build
  problem on FreeBSD.
* OpenSSL 0.9.7d bundled with Windows self-install.
	
2004.03.13 -- Version 1.6-rc3

* Minor Windows fixes for --ip-win32 dynamic, relating to
  the way the TAP-Win32 driver responds to a DHCP request
  from the Windows DHCP client.
* The net_gateway environmental variable wasn't being
  set correctly for called scripts (Paul Zuber).
* Added code to determine the default gateway on FreeBSD,
  allowing the --redirect-gateway option to work
  (Juan Rodriguez Hervella).
	
2004.03.04 -- Version 1.6-rc2

* Fixed bug in Windows version where the NetBIOS node-type
  DHCP option might have been passed even if it was not
  specified.
* Fixed bug in Windows version introduced in 1.6-rc1, where
  DHCP timeout would be set to 0 seconds if --ifconfig option
  was used and --ip-win32 option was not explicitly specified.
* Added some new --dhcp-option types for Windows version.

2004.03.02 -- Version 1.6-rc1

* For Windows, make "--ip-win32 dynamic" the default.
* For Windows, make "--route-delay 10" the default
  unless --ip-win32 dynamic is not used or --route-delay
  is explicitly specified.
* L_TLS mutex could have been left in a locked state
  for certain kinds of TLS errors.
	
2004.02.22 -- Version 1.6-beta7
	
* Allow scheduling priority increase (--nice) together
  with UID/GID downgrade (--user/--group).
* Code that causes SIGUSR1 restart on TLS errors in TCP
  mode was not activated in pthread builds.
* Save the certificate serial number in an environmental
  variable called tls_serial_{n} prior to calling the
  --tls-verify script.  n is the current cert chain level.
* Added NetBSD IPv6 tunnel capability (also requires
  a kernel patch) (Horst Laschinsky).
* Fixed bug in checking the return value of the nice()
  function (Ian Pilcher).
* Bug fix in new FreeBSD IPv6 over TUN code which was
  originally added in 1.6-beta5 (Nathanael Rensen).
* More Socks5 fixes -- extended the struct frame
  infrastructure to accomodate proxy-based encapsulation
  overhead.
* Added --dhcp-option to Windows version for setting
  adapter properties such as WINS & DNS servers.
* Use a default route-delay of 5 seconds when
  --ip-win32 dynamic is specified (only applicable when
  --route-delay is not explicitly specified).
* Added "log_append" registry variable to control
  whether the OpenVPN service wrapper on Windows
  opens log files in append (log_append="1") or
  truncate (log_append="0") mode.  The default
  is truncate.

2004.02.05 -- Version 1.6-beta6

* UDP over Socks5 fix to accomodate Socks5 encapsulation
  overhead (Christof Meerwald).
* Minor --ip-win32 dynamic tweaks (use long lease time,
  invalidate existing lease with DHCPNAK).

2004.02.01 -- Version 1.6-beta5

* Added Socks5 proxy support (Christof Meerwald).
* IPv6 tun support for FreeBSD (Thomas Glanzmann).
* Special TAP-Win32 debug mode for Windows self-install that was
  enabled in beta4 is now turned off.
* Added some new Solaris notes to INSTALL (Koen Maris).
* More work on --ip-win32 dynamic.

2004.01.27 -- Version 1.6-beta4

* For this beta, the Windows self-install is a debug version
  and will run slower -- use only for testing.
* Reverted the --ip-win32 default back to 'ipapi'
  from 'dynamic'.
* Added the offset parameter to '--ip-win32 dynamic' which
  can be used to control the address of the masqueraded
  DHCP server which replies to Windows DHCP requests.
* Added a wait/nowait option to --inetd (nowait can only
  be used with TCP sockets, TLS authentication, and over
  a bridged configuration -- see FAQ for more info)
  (Stefan `Sec` Zehl).
* Added a build-time capability where TAP-Win32 driver
  debug messages can be output by OpenVPN at --verb 6
  or higher.

2004.01.20 -- Version 1.6-beta2

* Added ./configure --enable-iproute2 flag which
  uses iproute2 instead of route + ifconfig --
  this is necessary for the LEAF Linux distro
  (Martin Hejl).
* Added renewal-time and rebind-time to set of
  DHCP options returned by the TAP-Win32 driver when
  "--ip-win32 dynamic" is used.
	
2004.01.14 -- Version 1.6-beta1

* Fixed --proxy bug that sometimes caused plaintext
  control info generated by the proxy prior to http
  CONNECT method establishment to be incorrectly
  parsed as OpenVPN data.
* For Windows version, implemented the
  "--ip-win32 dynamic" method and made it the default.
  This method sets the TAP-Win32 adapter IP address
  and netmask by replying to the kernel's DHCP queries.
  See the man page for more detailed info.
* Added --connect-retry parameter which controls
  the time interval (in seconds) between connect()
  retries when --proto tcp-client is used.  Previously,
  this value was hardcoded to 5 seconds, and still
  defaults as such.
* --resolv-retry can now be used with a parameter
  of "infinite" to retry indefinitely.
* Added SSL_CTX_use_certificate_chain_file() to ssl.c
  for support of multi-level certificate chains
  (Sten Kalenda).
* Fixed --tls-auth incompatibility with 1.4.x and earlier
  versions of OpenVPN when the passphrase file is an
  OpenVPN static key file (as generated by --genkey).
* Added shell-escape support in config files using
  the backslash character ("\") so that (for example)
  double quotes can be passed to the shell.
* Added "contrib" subdirectory on tarball, source zip,
  and CVS containing user-submitted contributions.
* Added an optional patch to the Redhat init script to
  allow the configuration file directory to be a
  multi-level directory hierarchy (Farkas Levente).
  See contrib/multilevel-init.patch
* Added some scripts and documentation on using
  Linux "fwmark" iptables rules to enable
  fine-grained routing control over the VPN
  (Sean Reifschneider, <<EMAIL>>).
  See contrib/openvpn-fwmarkroute-1.00

2003.11.20 -- Version 1.5.0

* Minor documentation changes.

2003.11.04 -- Version 1.5-beta14

* Fixed build problem with ./configure --disable-ssl
  that was reported on Debian woody.
* Fixed bug where --redirect-gateway could not be used
  together with --resolv-retry.

2003.11.03 -- Version 1.5-beta13

* Added CRL (certificate revocation list) capability using
  --crl-verify option (Stefano Bracalenti).
* Added --replay-window option for variable replay-protection
  window sizes.
* Fixed --fragment bug which might have caused certain large
  packets to be sent unfragmented.
* Modified --secret and --tls-auth to permit different cipher and
  HMAC keys to be used for each data flow direction.  Also
  increased static key file size generated by --genkey from
  1024 to 2048 bits, where 512 bits each are reserved for
  send-HMAC, encrypt, receive-HMAC, and decrypt.  Key file forward
  and backward compatibility is maintained.  See --secret option
  documentation on the man page for more info.
* Added --tls-remote option (Teemu Kiviniemi).
* Fixed --tls-cipher documention regarding correct delimiter
  usage (Teemu Kiviniemi).
* Added --key-method option for selecting alternative data
  channel key negotiation methods.  Method 1 is the default.
  Method 2 has been added (see man page for more info).
* Added French translation of HOWTO to web site
  (Guillaume Lehmann).
* Fixed problem caused by late resolver library load on
  certain platforms when --resolv-retry and --chroot are
  used together (Teemu Kiviniemi).
* In TCP mode, all decryption or TLS errors will abort the current
  connection (this is not done in UDP mode because UDP is
  "connectionless").
* Fixed a TCP client reconnect bug that only occurs on the
  BSDs, where connect() fails with an invalid argument.  This
  bug was partially (but not completely) fixed in beta7.
* Added "route_net_gateway" environmental variable which contains
  the pre-existing default gateway address from the routing table
  (there's no standard API for getting the default gateway, so
  right now this feature only works on Windows or Linux).
* Renamed the "route_default_gateway" enviromental variable to
  "route_vpn_gateway" -- this is the remote VPN endpoint.
* The special keywords vpn_gateway, net_gateway, and remote_host
  can now be used for the network or gateway components of the
  --route option.  See the man page for more info.
* Added the --redirect-gateway option to configure the VPN
  as the default gateway (implemented on Linux and Windows only).
* Added the --http-proxy option with basic authentication
  support for use in TCP client mode.  Successfully tested
  using Squid as the HTTP proxy, with and without authentication.

2003.10.12 -- Version 1.5-beta12

* Fixed Linux-only bug in --mktun and --rmtun which was
  introduced around beta8 or so, which would cause
  an error such as "I don't recognize device tun0 as a
  tun or tap device1".
* Added --ifconfig-nowarn option to disable options
  consistency warnings about --ifconfig parameters.
* Don't allow any kind of sequence number backtracking or
  message reordering when in TCP mode.
* Changed beta naming convention to use '_' (underscore)
  rather than '-' (dash) to pacify rpmbuild.
	
2003.10.08 -- Version 1.5-beta11

* Modified code in the Windows version which sets the IP address
  and netmask of the TAP-Win32 adapter using the IP Helper API.
  Most of the changes involve better error recovery when
  the IP Helper API returns an error status.  See the
  manual page entry on --ip-win32 for more info.

2003.10.08 -- Version 1.5-beta10

* Added getpass() function for Windows version so that --askpass
  option works correctly (Stefano Bracalenti).
* Added reboot advisory to end of Win32 install script.
* Changed crypto code to use pseudo-random IVs rather than
  carrying forward the IV state from the previous packet.
  This is in response to item 2 in the following document:
  http://www.openssl.org/~bodo/tls-cbc.txt which points
  out weaknesses in TLS's use of the same IV carryforward
  approach.  This change does not break protocol compatibility
  with previous versions of OpenVPN.
* Made a change to the crypto replay protection code to also
  protect against certain kinds of packet reordering attacks.
  This change does not break protocol compatibility with
  previous versions of OpenVPN.
* Added --ip-win32 option to provide several choices for
  setting the IP address on the TAP-Win32 adapter.
* #ifdefed out non-CBC crypto modes by default.
* Added --up-delay option to delay TUN/TAP open and --up script
  execution until after connection establishment.  This option
  replaces the earlier windows-only option --tap-delay.
  
2003.10.01 -- Version 1.5-beta9

* Fixed --route-noexec bug where option was not parsed correctly.
* Complain if --dev tun is specified without --ifconfig on Windows.
* Fixed bug where TCP connections on windows would sometimes cause
  an assertion failure.
* Added a new flag to TAP-Win32 advanced properties that allows one
  to set the adapter to be always "connected" even when an OpenVPN
  process doesn't have it open.  The default behavior is to report
  a media status of connected only when an OpenVPN process has the
  adapter open.
* Rebuilt the Windows self-install distribution with OpenSSL 0.9.7c
  DLLs in response to an OpenSSL security advisory.

2003.09.30 -- Version 1.5-beta8

* Extended the --ifconfig option to work on tap devices as well
  as tun devices.
* Implemented the --ifconfig option for Windows, by calling the
  netsh tool.
* By default, do an "arp -d *" on Windows after TAP-Win32 open to
  refresh the MAC cache.  This behaviour can be disabled with
  --no-arp-del.
* On Windows, allow the --dev-node parameter (which specifies
  the name of the TAP-Win32 adapter) to be omitted in cases where
  there is a single TAP-Win32 adapter on the system which can be
  assumed to be the default.
* Modified the diagnostic --verb 5 debugging level to print 'R'
  for TCP/UDP read, 'W' for TCP/UDP write, 'r' for TUN/TAP read,
  and 'w' for TUN/TAP write.
* Conditionalize OpenBSD read_tun and write_tun based on tun or tap
  mode.
* Added IPv6 tun support to OpenBSD (Thomas Glanzmann).
* Make the --enable-mtu-dynamic ./configure option enabled by
  default.
* Deprecated the --mtu-dynamic run-time option, in favor of
  --fragment.
* DNS names can now be used as --ifconfig parameters.
* Significant work on TAP-Win32 driver to bring up to SMP standards.
* On Windows, fixed dangling IRP problem if TAP-Win32 driver is
  unloaded or disabled, while a user-space process has it open.
* On Windows, if --tun-mtu is not specified, it will be read from
  the TAP-Win32 driver via ioctl.
* On Windows, added TAP-Win32 driver status info to "F2" keyboard
  signal (only when run from a console window).
* Added --mssfix option to control TCP MSS size (YANO Hirokuni).
* Renamed --mtu-dynamic option to --fragment to more accurately
  reflect its function.  Fragment accepts a single parameter which
  is the upper limit on acceptable UDP packet size.
* Changed default --tun-mtu-extra parameter to 32 from 64.
* Eliminated reference to malloc.o in configure.ac.
* Added tun device emulation to the TAP-Win32 driver.
* Added --route and related options.
* Added init script for SuSE Linux (Frank Plohmann).
* Extended option consistency check between peers to function
  in all crypto modes, including static-key and cleartext modes.
  Previously only TLS mode was supported.  Disable with
  --disable-occ.
* Overall, increased the amount of configuration option sanity
  checking, especially of networking parameters.
* Added --mtu-test option for empirical MTU measurement.
* Added Windows-only option --tap-delay to not set the TAP-Win32
  adapter media state to 'connected' until TCP/UDP connection
  establishment with peer.
* Slightly modified --route/--route-delay semantics so that when
  --route is given without --route-delay, routes are added
  immediately after tun/tap device open.  When --route-delay is
  specified, routes will be added n seconds after connection
  initiation, where n is the --route-delay parameter (which
  can be set to 0).	
* Made TCP framing error into a non-fatal error that triggers a
  connection reset.

2003.08.28 -- Version 1.5-beta7

* Fixed bug that caused OpenVPN not to respond to exit/restart
  signals when --resolv-retry is used and a local or remote DNS
  name cannot be resolved.
* Exported a series of environmental variables with useful
  info for scripts.  See man page for more info.  Based
  on a suggestion by Anthony Ciaravalo.
* Moved TCP/UDP socket bind to a point in the initialization
  before the --up script gets called.  This is desirable
  because (a) a socket bind failure will happen before
  daemonization, allowing an error status code to be returned
  to the shell and (b) the possibility is eliminated of a
  socket bind failure causing the --up script to be run
  but not the --down script.  This change has a side effect
  that --resolv-retry will no longer work with --local.
* Fixed bug where if an OpenVPN TCP server went down and back
  up again, Solaris or FreeBSD clients would fail to reconnect
  to it.
* Fixed bug that prevented OpenVPN from being run by
  inetd/xinetd in TCP mode.
* Added --log and --log-append options for logging messages to
  a file.
* On Windows, check that the current user is a member of the
  Administrator group before attempting install or uninstall.

2003.08.16 -- Version 1.5-beta6

* Fixed TAP-Win32 driver to properly increment the Rx/Tx count.

2003.08.14 -- Version 1.5-beta5

* Added user-configurability of the TAP-Win32 adapter MTU
  through the adapter advanced properties page.
* Added Windows Service support.
* On Windows, added file association and right-clickability
  for .ovpn files (OpenVPN config files).

2003.08.05 -- Version 1.5-beta4

* Extra refinements and error checking added to Windows
  NSIS install script.
	
2003.08.05 -- Version 1.5-beta3
	
* Added md5.h include to crypto.c to fix build problem on
  OpenBSD.
* Created a Win32 installer using NSIS.
* Removed DelService command from TAP-Win32 INF file.  It appears
  to be not necessary and it interfered with the ability to
  uninstall and reinstall the driver without needing to reboot.
* On Windows version, added "addtap" and "deltapall" batch
  files to add and delete TAP-Win32 adapter instances.

2003.07.31 -- Version 1.5-beta2
	
* Renamed INSTALL.w32 to INSTALL-win32.txt and reformatted
  in Windows ASCII so it's easier to click and view.
* Added postscript and PDF versions of the HOWTO to the web
  site (C R Zamana).
* Merged Michael Clarke's stability patch into TAP-Win32
  driver which appears to fix the suspend/resume driver bug
  and significantly improve driver stability.
* Added Christof Meerwald's Media Status patch to the
  TAP-Win32 driver which shows the TAP adapter to be
  disconnected when OpenVPN is not running.
* Moved socket connect and TCP server listen code to a later
  point in openvpn() function so that the TCP server listen
  state is entered after daemonization.
* Added keyboard shortcuts to simulate signals in the Windows
  version, see the window title bar for descriptions.

2003.07.24 -- Version 1.5-beta1
	
* Added TCP support via the new --proto option.
* Renamed udp-centric options such as --udp-mtu to
  --link-mtu (old option names preserved for compatibility).
* Ported to Windows 2000 + XP using mingw and a TAP driver
  derived from the Cipe-Win32 project by Damion K. Wilson.
* Added --show-adapters flag for windows version.
* Reworked the SSL/TLS packet acknowledge code to better
  handle certain corner cases.
* Turned off the default enabling of IP forwarding in the
  sample-scripts/openvpn.init script for Redhat.
  Forwarding can be enabled by users in their --up scripts
  or firewall config.
* Added --up-restart option based on suggestion from Sean
  Reifschneider.
* If --dev tap or --dev-type tap is specified, --tun-mtu
  defaults to 1500 and --tun-mtu-extra defaults to 64.
* Enabled --verb 5 debugging mode that prints 'R' and 'W'
  for each packet read or write on the TCP/UDP socket.

2003.08.04 -- Version 1.4.3

* Added md5.h include to crypto.c
  to fix build problem on OpenBSD.

2003.07.15 -- Version 1.4.2

* Removed adaptive bandwidth from
  --mtu-dynamic -- its absence appears
  to work better than its existence (*******).
* Minor changes to --shaper to fix long
  retransmit timeouts at low bandwidth
  (*******).
* Added LOG_RW flag to openvpn.h for
  debugging (*******).
* Silenced spurious configure warnings (*******).
* Backed out --dev-name patch, modified --dev
  to offer equivalent functionality (*******).
* Added an optional parameter to --daemon and
  --inetd to support the passing of a custom
  program name to the system logger (*******).
* Add compiled-in options to the program title
  (*******).
* Coded the beginnings of a WIN32 port (*******).
* Succeeded in porting to Win32 Mingw environment
  and running loopback tests (*******).  Still
  need a kernel driver for full Win32
  functionality.
* Fixed a bug in error.h where
  HAVE_CPP_VARARG_MACRO_GCC was misspelled.
  This would have caused a significant slowdown
  of OpenVPN when built by compilers that
  lack ISO C99 vararg macros (*******).
* Created an init script for Gentoo Linux
  in ./gentoo directory (*******).

2003.05.15 -- Version 1.4.1

* Modified the Linux 2.4 TUN/TAP open code to
  fall back to the 2.2 TUN/TAP interface if the
  open or ioctl fails.
* Fixed bug when --verb is set to 0 and non-fatal
  socket errors occur, causing 100% CPU utilization.
  Occurs on platorms where
  EXTENDED_SOCKET_ERROR_CAPABILITY is defined,
  such as Linux 2.4.
* Fixed typo in tun.c that was preventing
  OpenBSD build.
* Added --enable-mtu-dynamic configure option
  to enable --mtu-dynamic experimental option.
	
2003.05.07 -- Version 1.4.0

* Added --replay-persist feature to allow replay
  protection across sessions.
* Fixed bug where --ifconfig could not be used
  with --tun-mtu.
* Added --tun-mtu-extra parameter to deal with
  the situation where a read on a TUN/TAP device
  returns more data than the device's MTU size.
* Fixed bug where some IPv6 support code for
  Linux was not being properly ifdefed out for
  Linux 2.2, causing compile errors.
* Added OPENVPN_EXIT_STATUS_x codes to
  openvpn.h to control which status value
  openvpn returns to its caller (such as
  a shell or inetd/xinetd) for various conditions.
* Added OPENVPN_DEBUG_COMMAND_LINE flag to
  openvpn.h to allow debugging in situations
  where stdout, stderr, and syslog cannot be used
  for message output, such as when OpenVPN is
  instantiated by inetd/xinetd.
* Removed owner-execute permission from file
  created by static key generator (Herbert Xu
  and Alberto Gonzalez Iniesta).
* Added --passtos option to allow IPv4 TOS bits
  to be passed from TUN/TAP input packets to
  the outgoing UDP socket (Craig Knox).
* Added code to prevent open socket file descriptors
  from being accessible to called scripts.
* Added --dev-name option (Christian Lademann).
* Added --mtu-disc option for manual control
  over MTU options.
* Show OS MTU value on UDP socket write failures
  (linux only).
* Numerous build system and portability
  fixes (Matthias Andree).
* Added better sensing of compiler support for
  variable argument macros, including (a) gcc
  style, (b) ISO C 1999 style, and (c) no support.
* Removed generated files from CVS.  Note INSTALL
  file for new CVS build commands.
* Changed certain internal symbol names
  for C standards compliance.
* Added TUN/TAP open code to cycle dynamically
  through unit numbers until it finds a free
  unit (based on code from Thomas Gielfeldt
  and VTun).
* Added dynamic MTU and fragmenting infrastructure
  (Experimental).  Rebuild with FRAGMENT_ENABLE
  defined to enable.
* Minor changes to SSL/TLS negotiation, use
  exponential backoff on retransmits, and use
  a smaller MTU size (note that no protocol
  changes have been made which would break
  compatibility with 1.3.x).
* Added --enable-strict-options flag
  to ./configure.  This option will cause
  a more strict check for options compatibility
  between peers when SSL/TLS negotiation is used,
  but should only be used when both OpenVPN peers
  are of the same version.
* Reorganization of debugging levels.
* Added a workaround in configure.ac for
  default SSL header location on Linux
  to fix RH9 build problem.
* Fixed potential deadlock when pthread support
  is used on OSes that allocate a small socketpair()
  message buffer.
* Fixed openvpn.init to be sh compliant
  (Bishop Clark).
* Changed --daemon to wait until all
  initialization is finished before becoming a
  daemon, for the benefit of initialization
  scripts that want a useful return status from
  the openvpn command.
* Made openvpn.init script more robust, including
  positive indication of initialization errors
  in the openvpn daemon and better sanity checks.
* Changed --chroot to wait until initialization
  is finished before calling chroot(), and allow
  the use of --user and --group with --chroot.
* When syslog logging is enabled (--daemon or
  --inetd), set stdin/stdout/stderr to point
  to /dev/null.
* For inetd instantiations, dup socket descriptor
  to a >2 value.
* Fixed bug in verify-cn script, where test would
  incorrectly fail if CN=x was the last component
  of the X509 composite string (Anonymous).
* Added Markus F.X.J. Oberhumer's special
  license exception to COPYING.

2002.10.23 -- Version 1.3.2

* Added SSL_CTX_set_client_CA_list call
  to follow the canonical form for TLS initialization
  recommended by the OpenSSL docs.  This change allows
  better support for intermediate CAs and has no impact
  on security.
* Added build-inter script to easy-rsa package, to
  facilitate the generation of intermediate CAs.
* Ported to NetBSD (Dimitri Goldin).
* Fixed minor bug in easy-rsa/sign-req.  It refers to
  openssl.cnf file, instead of $KEY_CONFIG, like all
  other scripts (Ernesto Baschny).
* Added --days 3650 to the root CA generation command
  in the HOWTO to override the woefully small 30 day
  default (Dominik 'Aeneas' Schnitzer).
* Fixed bug where --ping-restart would sometimes
  not re-resolve remote DNS hostname.
* Added --tun-ipv6 option and related infrastructure
  support for IPv6 over tun.
* Added IPv6 over tun support for Linux (Aaron Sethman).
* Added FreeBSD 4.1.1+ TUN/TAP driver notes to
  INSTALL (Matthias Andree).
* Added inetd/xinetd support (--inetd) including
  documentation in the HOWTO.
* Added "Important Note on the use of commercial certificate
  authorities (CAs) with OpenVPN" to HOWTO based on
  issues raised on the openvpn-users list.

2002.07.10 -- Version 1.3.1

* Fixed bug in openvpn.spec and openvpn.init
  which caused RPM upgrade to fail.

2002.07.10 -- Version 1.3.0

* Added --dev-node option to allow explicit selection of
  tun/tap device node.
* Removed mlockall call from child thread, as it doesn't
  appear to be necessary (child thread inherits mlockall
  state from parent).
* Added --ping-timer-rem which causes timer for --ping-exit
  and --ping-restart not to run unless we have a remote IP
  address.
* Added condrestart to openvpn.init and openvpn.spec
  (Bishop Clark).
* Added --ifconfig case for FreeBSD (Matthias Andree).
* Call openlog with facility=LOG_DAEMON (Matthias Andree).
* Changed LOG_INFO messages to LOG_NOTICE.
* Added warning when key files are group/others accessible.
* Added --single-session flag for TLS mode.
* Fixed bug where --writepid would segfault if used with
  an invalid filename.
* Fixed bug where --ipchange status message was formatted
  incorrectly.
* Print more concise error message when system() call
  fails.
* Added --disable-occ option.
* Added --local, --remote, and --ifconfig options sanity
  check.
* Changed default UDP MTU to 1300 and TUN/TAP MTU to
  1300.
* Successfully tested with OpenSSL 0.9.7 Beta 2.
* Broke out debug level definitions to errlevel.h
* Minor documentation and web site changes.
* All changes maintain protocol compatibility
  with OpenVPN versions since 1.1.0, however default
  MTU changes will require setting the MTU explicitly
  by command line option, if you want 1.3.0 to
  communicate with previous versions.

2002.06.12 -- Version 1.2.1

* Added --ping-restart option to restart
  connection on ping timeout using SIGUSR1
  logic (Matthias Andree).
* Added --persist-tun, --persist-key,
  --persist-local-ip, and --persist-remote-ip
  options for finer-grained control over SIGUSR1
  and --ping-restart restarts.  To
  replicate previous SIGUSR1 functionality,
  use --persist-remote-ip.
* Changed residual IV fetching code to take
  IV from tail of ciphertext.
* Added check to make sure that CFB or OFB
  cipher modes are only used with SSL/TLS
  authentication mode, and added a caveat
  to INSTALL.
* Changed signal handling during initialization
  (including re-initialization during restarts)
  to exit on SIGTERM or SIGINT and ignore other
  signals which would ordinarily be caught.
* Added --resolv-retry option to allow
  retries on hostname resolution.
* Expanded the --float option to also
  allow dynamic changes in source port number
  on incoming datagrams.
* Added --mute option to limit repetitive
  logging of similar message types.
* Added --group option to downgrade GID
  after initialization.
* Try to set ifconfig path automatically
  in configure.
* Added --ifconfig code for Mac OS X
  (Christoph Pfisterer).
* Moved "Peer Connection Initiated" message
  to --verb level 1.
* Successfully tested with
  OpenSSL 0.9.7 Beta 1 and AES cipher.
* Added RPM notes to INSTALL.
* Added ACX_PTHREAD (from the autoconf
  macro archive) to configure.ac
  to figure out the right pthread
  options for a given platform.
* Broke out macro definitions from
  configure.ac to acinclude.m4.
* Minor changes to docs and HOWTO.
* All changes maintain protocol compatibility
  with OpenVPN versions since 1.1.0.

2002.05.22 -- Version 1.2.0

* Added configuration file support via
  the --config option.
* Added pthread support to improve latency.
  With pthread support, OpenVPN
  will offload CPU-intensive tasks such as RSA
  key number crunching to a background thread
  to improve tunnel packet forwarding
  latency.  pthread support can be enabled
  with the --enable-pthread configure option.
  Pthread support is currently available
  only for Linux and Solaris.
* Added --dev-type option so that tun/tap
  device names don't need to begin with
  "tun" or "tap".
* Added --writepid option to write main
  process ID to a file.
* Numerous portability fixes to ease
  porting to other OSes including changing
  all network types to uint8_t and uint32_t,
  and not assuming that time_t is 32 bits.
* Backported to OpenSSL 0.9.5.
* Ported to Solaris.
* Finished OpenBSD port except for
  pthread support.
* Added initialization script:
  sample-scripts/openvpn.init
  (Douglas Keller)
* Ported to Mac OS X (Christoph Pfisterer).
* Improved resilience to DoS attacks when
  TLS mode is used without --remote or
  --tls-auth, or when --float is used
  with --remote.  Note however that the best
  defense against DoS attacks in TLS mode
  is to use --tls-auth.
* Eliminated automake/autoconf dependency
  for non-developers.
* Ported configure.in to configure.ac
  and autoconf 2.50+.
* SIGHUP signal now causes OpenVPN to restart
  and re-read command line and or config file,
  in conformance with canonical daemon behaviour.
* SIGUSR1 now does what SIGHUP did in
  version 1.1.1 and earlier -- close and reopen
  the UDP socket for use when DHCP changes
  host's IP address and preserve most recently
  authenticated peer address without rereading
  config file.
* SIGUSR2 added -- outputs current statistics,
  including compression statistics.
* All changes maintain protocol compatibility
  with 1.1.1 and 1.1.0.

2002.04.22 -- Version 1.1.1
	
* Added --ifconfig option to automatically configure
  TUN device.
* Added inactivity disconnect (--inactive
  and --ping-exit options).
* Added --ping option to keep stateful firewalls
  from timing out.
* Added sanity check to command line parser to
  err if any TLS options are used in non-TLS mode.
* Fixed build problem with compiler environments that
  define printf as a macro.
* Fixed build problem on linux systems that have
  an integrated TUN/TAP driver but lack the persistent
  tunnel feature (TUNSETPERSIST).  Some linux kernels
  >= 2.4.0 and < 2.4.7 fall into this category.
* Changed all calls to EVP_CipherInit to use explicit
  encrypt/decrypt mode in order to fix problem with
  IDEA-CBC and AES-256-CBC ciphers.
* Minor changes to control channel transmit limiter
  algorithm to fix problem where TLS control channel
  might not renegotiate within the default 60 second window.
* Simplified man page examples by taking advantage
  of the new --ifconfig option.
* Minor changes to configure.in to check more
  rigourously for OpenSSL 0.9.6 or greater.
* Put back openvpn.spec, eliminated
  openvpn.spec.in.
* Modified openvpn.spec to reflect new automake-based
  build environment (Bishop Clark).
* Other documentation changes.
* Added --test-crypto option for debugging.
* Added "missing" and "mkinstalldirs" automake
  support files.


2002.04.09 -- Version 1.1.0

* Strengthened replay protection and IV handling,
  extending it fully to both static key and
  TLS dynamic key exchange modes.
* Added --mlock option to disable paging and ensure that key
  material and tunnel data is never paged to disk.
* Added optional traffic shaping feature to cap the maximum
  data rate of the tunnel.
* Converted to automake (The Platypus Brothers 2002-04-01).
* Ported to OpenBSD by Janne Johansson.
* Added --tun-af-inet option to work around an incompatibility
  between Linux and BSD tun drivers.
* Sequence number-based replay protection using the
  IPSec sliding window model is now the default,
  disable with --no-replay.
* Explicit IV is now the default, disable with --no-iv.
* Disabled all cipher modes except CBC, CFB, and OFB.
* In CBC mode, use explicit IV and carry forward residuals,
  using IPSec model.
* In CFB/OFB mode, IV is timestamp, sequence number.
* Eliminated --packet-id, --timestamp, and max-delta parameter to
  the --tls-auth option as they are now supplanted by improved
  replay code which is enabled by default.
* Eliminated --rand-iv as it is now obsolete with improved
  IV code.
* Eliminated --reneg-err option as it increases vulnerability
  to DoS attacks.
* Added weak key check for DES ciphers.
* --tls-freq option is no longer specified on the command line,
  instead it now inherits its parameter from the
  --tls-timeout option.
* Fixed bug that would try to free memory on exit that was
  never malloced if --comp-lzo was not specified.
* Errata fixed in the man page examples: "test-ca" should be
  "tmp-ca".
* Updated manual page.
* Preliminary work in porting to OpenSSL 0.9.7.
* Changed license to allowing linking with OpenSSL.

2002.03.29 -- Version 1.0.3

* Fixed a problem in configure with library ordering on the
  command line.

2002.03.28 -- Version 1.0.2

* Improved the efficiency of the inner event loop.
* Fixed a minor bug with timeout handling.
* Improved the build system to build on RH 6.2 through 7.2.
* Added an openvpn.spec file for RPM builders (Bishop Clark).

2002.03.23 -- Version 1.0

* Added TLS-based authentication and key exchange.
* Added gremlin mode to stress test.
* Wrote man page.

2001.12.26 -- Version 0.91

* Added any choice of cipher or HMAC digest.

2001.5.13 -- Version 0.90

* Initial release.
* IP tunnel over UDP, with blowfish cipher and SHA1 HMAC signature.
