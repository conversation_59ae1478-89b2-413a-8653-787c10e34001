OpenVPN fwmark Routing
<PERSON>, <<EMAIL>>
Thursday November 27, 2003
==========================

These scripts can be used with OpenVPN up and down scripts to set up
routing on a Linux system such that the VPN traffic is sent via normal
network connectivity, but other traffic to that network runs over the VPN.
The idea is to allow encryption of data to the network the remote host is
on, without interfering with the VPN traffic.  You can't simply add a route
to the remote network, becaues that will cause the VPN traffic to also try
to run over the VPN, and breaks the VPN.

These scripts use the Linux "fwmark" iptables rules to specify routing
based not only on IP address, but also by port and protocol.  This allows
you to effectively say "if the packet is to this IP address on this port
using this protocol, then use the normal default gateway, otherwise use the
VPN gateway.

This is set up on the client VPN system, not the VPN server.  These scripts
also set up all ICMP echo-responses to run across the VPN.  You can
comment the lines in the scripts to disable this, but I find this useful
at coffee shops which have networks that block ICMP.

To configure this, you need to set up these scripts as your up and down
scripts in the config file.  You will need to set these values in the
config file:

   up /etc/openvpn/fwmarkroute.up
   down /etc/openvpn/fwmarkroute.down
   up-restart
   up-delay

   setenv remote_netmask_bits 24

Note: For this to work, you can't set the "user" or "group" config options,
because then the scripts will not run as root.

The last setting allows you to control the size of the network the remote
system is on.  The remote end has to be set up to route, probably with
masquerading or NAT.  The network this netmask relates to is calculated
using the value of "remote" in the conf file.

Sean
