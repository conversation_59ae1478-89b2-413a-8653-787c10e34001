# An xinetd configuration file for OpenVPN.
#
# This file should be renamed to openvpn or something suitably
# descriptive and copied to the /etc/xinetd.d directory.
# xinetd can then be made aware of this file by restarting
# it or sending it a SIGHUP signal.
#
# For each potential incoming client, create a separate version
# of this configuration file on a unique port number.  Also note
# that the key file and ifconfig endpoints should be unique for
# each client.  This configuration assumes that the OpenVPN
# executable and key live in /root/openvpn.  Change this to fit
# your environment.

service openvpn_1
{
        type            = UNLISTED
        port            = 1194
        socket_type     = dgram
        protocol        = udp
        wait            = yes
        user            = root
        server          = /root/openvpn/openvpn
        server_args     = --inetd --dev tun --ifconfig ******** ******** --secret /root/openvpn/key --inactive 600 --user nobody
}
