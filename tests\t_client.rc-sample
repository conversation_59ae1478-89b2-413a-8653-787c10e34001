#
# this is sourced from t_client.sh and defines which openvpn client tests
# to run
#
# (sample config, copy to t_client.rc and adapt to your environment)
#
#
# define these - if empty, no tests will run
#
top_srcdir="${top_srcdir:-..}"
CA_CERT="${top_srcdir}/sample/sample-keys/ca.crt"
CLIENT_KEY="${top_srcdir}/sample/sample-keys/client.key"
CLIENT_CERT="${top_srcdir}/sample/sample-keys/client.crt"
#FPING_EXTRA_ARGS="-t 1000"

# Load EXPECT_IFCONFIG* parameters from cache
if [ -r "${top_builddir}/t_client_ips.rc" ]; then
    . "${top_builddir}/t_client_ips.rc"
else
    echo "NOTICE: missing t_client_ips.rc will be auto-generated"
fi

#
# remote host (used as macro below)
#
REMOTE=mytestserver
#
# tests to run (list suffixes for config stanzas below)
#
TEST_RUN_LIST="1 2"

#
# use "sudo" (etc) to give openvpn the necessary privileges
# if this is not active, "make check" must be run as root
#
#RUN_SUDO=sudo

#
# base confic that is the same for all the p2mp test runs
#
OPENVPN_BASE_P2MP="--client --ca $CA_CERT \
	--cert $CLIENT_CERT --key $CLIENT_KEY \
	--remote-cert-tls server --nobind --comp-lzo --verb 3"

# base config for p2p tests
#
OPENVPN_BASE_P2P="..."

#
#
# now define the individual tests - all variables suffixed with _1, _2 etc
# will be used in test run "1", "2", etc.
#
# if something is not defined here, the corresponding test is not run
#
# possible test options:
#
# RUN_TITLE_x="what is being tested on here" (purely informational)
# OPENVPN_CONF_x = "how to call ./openvpn" [mandatory]
# EXPECT_IFCONFIG4_x = "this IPv4 address needs to show up in ifconfig"
# EXPECT_IFCONFIG6_x = "this IPv6 address needs to show up in ifconfig"
# PING4_HOSTS_x = "these hosts musts ping when openvpn is up (IPv4 fping)"
# PING6_HOSTS_x = "these hosts musts ping when openvpn is up (IPv6 fping6)"
#
# Test 1: UDP / p2mp tun
#   specify IPv4+IPv6 addresses expected from server and ping targets
#
RUN_TITLE_1="testing tun/udp/ipv4+ipv6"
OPENVPN_CONF_1="$OPENVPN_BASE_P2MP --dev tun --proto udp --remote $REMOTE --port 51194"
PING4_HOSTS_1="*********** **********"
PING6_HOSTS_1="2001:db8::1 2001:db8:a050::1"

# Test 2: TCP / p2mp tun
#
RUN_TITLE_2="testing tun/tcp/ipv4+ipv6"
OPENVPN_CONF_2="$OPENVPN_BASE_P2MP --dev tun --proto tcp --remote $REMOTE --port 51194"
PING4_HOSTS_2="*********** **********"
PING6_HOSTS_2="2001:db8::1 2001:db8:a051::1"
#
# run command after openvpn initialization is done - here: delay 5 seconds
POSTINIT_CMD_2="sleep 5"

# Test 3: UDP / p2p tun
# ...

# Test 4: TCP / p2p tun
# ...

# Test 5: UDP / p2mp tap
# ...

# Test 6: TCP / p2mp tun
# ...

# Test 7: UDP / p2p tap
# ...

# Test 8: TCP / p2p tap
# ...

# Test 9: whatever you want to test... :-)
