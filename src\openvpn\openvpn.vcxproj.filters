﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="base64.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="buffer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="clinat.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="console.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="crypto.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="crypto_openssl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cryptoapi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="dhcp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="error.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="event.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fdmisc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="forward.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="fragment.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="gremlin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="helper.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="httpdigest.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="init.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="interval.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="list.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lladdr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="lzo.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="manage.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mbuf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="misc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mroute.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mss.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mstats.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mtcp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mtu.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="mudp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="multi.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ntlm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="occ.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="openvpn.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="options.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="otime.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="packet_id.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="perf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pf.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ping.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pkcs11.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pkcs11_openssl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="platform.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="plugin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="pool.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="proto.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="proxy.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ps.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="push.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="reliable.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="route.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="schedule.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="session_id.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="shaper.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="sig.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="socket.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="socks.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ssl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ssl_openssl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ssl_verify.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ssl_verify_openssl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="status.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tun.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="win32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="comp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="compstub.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="comp-lz4.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="argv.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="block_dns.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="console_builtin.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="tls_crypt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="base64.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="basic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="buffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="circ_list.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="clinat.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="comp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="comp-lz4.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="console.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="crypto.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="crypto_backend.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="crypto_openssl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cryptoapi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="dhcp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="errlevel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="error.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="event.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fdmisc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="forward-inline.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="forward.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fragment.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="gremlin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="helper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="httpdigest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="init.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="integer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="interval.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="list.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lladdr.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="lzo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="manage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mbuf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="memdbg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="misc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mroute.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mss.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mstats.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mtcp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mtu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="mudp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="multi.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ntlm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="occ-inline.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="occ.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="openvpn.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="options.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="otime.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="packet_id.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="perf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pf-inline.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ping-inline.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ping.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pkcs11.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pkcs11_backend.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="platform.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="plugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pool.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="proto.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="proxy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ps.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="push.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="pushlist.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="reliable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="route.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="schedule.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="session_id.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="shaper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="sig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="socket.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="socks.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ssl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ssl_backend.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ssl_common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ssl_openssl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ssl_verify.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ssl_verify_backend.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ssl_verify_openssl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="status.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="syshead.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tun.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="win32.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="compstub.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="argv.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="block_dns.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="tls_crypt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="openvpn_win32_resources.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>